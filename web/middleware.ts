import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath = path === "/login";

  // Get the authentication status from cookies
  const accessToken = request.cookies.get("access_token")?.value;

  // Redirect logic
  if (!accessToken && !isPublicPath) {
    // Redirect to login if trying to access a protected route without authentication
    return NextResponse.redirect(new URL("/login", request.url));
  }

  if (accessToken && isPublicPath) {
    // Redirect to dashboard if trying to access login while authenticated
    return NextResponse.redirect(new URL("/employee", request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
