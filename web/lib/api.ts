import {
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
} from "@/types/auth";
import { EmployeeListResponse, Employee } from "@/types/employee";
import { ScheduleListResponse } from "@/types/schedule";
import { redirect } from "next/navigation";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";

// Helper function to get access token from cookies
const getAccessToken = () => {
  return document.cookie
    .split("; ")
    .find((row) => row.startsWith("access_token="))
    ?.split("=")[1];
};

// Helper function to handle unauthorized errors
const handleUnauthorizedError = () => {
  // Clear tokens
  localStorage.removeItem("refresh_token");
  document.cookie =
    "access_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

  // Redirect to login page
  window.location.href = "/login";
};

// Helper function to handle API responses
const handleApiResponse = async (response: Response) => {
  if (response.status === 401) {
    handleUnauthorizedError();
    throw new Error("Unauthorized - Please log in again");
  }

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "API request failed");
  }

  return response.json();
};

export const api = {
  auth: {
    login: async (data: LoginRequest): Promise<LoginResponse> => {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    register: async (data: RegisterRequest): Promise<RegisterResponse> => {
      const response = await fetch(`${API_URL}/auth/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    logout: async () => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/auth/logout`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },

    getCurrentUser: async () => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/auth/me`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      const data = await handleApiResponse(response);
      if (!data.email) {
        throw new Error("User not authenticated");
      }
      const emailUsername = data.email.split("@")[0];
      return {
        id: data.id,
        email: data.email,
        name:
          data.name ||
          emailUsername.charAt(0).toUpperCase() + emailUsername.slice(1),
        role: data.role,
        company_id: data.company_id,
      };
    },
  },
  employees: {
    list: async (): Promise<EmployeeListResponse> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/employees`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
    create: async (data: {
      first_name: string;
      last_name: string;
      email: string;
      phone_number: string;
      password: string;
      company_id: string;
      employee_number?: string;
    }): Promise<Employee> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/employees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
    update: async (
      employeeId: string,
      data: Partial<{
        first_name: string;
        last_name: string;
        email: string;
        phone_number: string;
        password: string;
        employee_number?: string;
      }>
    ): Promise<Employee> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/employees/${employeeId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
    delete: async (employeeId: string): Promise<{ message: string }> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/employees/${employeeId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
  },
  timeSlots: {
    list: async () => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/time-slots`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
    update: async (id: number, data: any) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/time-slots/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
    create: async (data: any) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/time-slots`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
  },
  schedules: {
    listSchedules: async (params?: {
      employee_id?: string;
      created_by?: string;
      start_date?: string;
      end_date?: string;
    }): Promise<ScheduleListResponse> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      // Build query string if params exist
      let url = `${API_URL}/schedules`;
      if (params) {
        const queryParams = new URLSearchParams();
        if (params.employee_id)
          queryParams.append("employee_id", params.employee_id);
        if (params.created_by)
          queryParams.append("created_by", params.created_by);
        if (params.start_date)
          queryParams.append("start_date", params.start_date);
        if (params.end_date) queryParams.append("end_date", params.end_date);

        const queryString = queryParams.toString();
        if (queryString) {
          url += `?${queryString}`;
        }
      }

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
    create: async (data: any) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/schedules`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
    update: async (id: number, data: any) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/schedules/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },
    delete: async (id: number) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(`${API_URL}/schedules/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
  },
  scheduleLogs: {
    listAdmin: async (params: {
      page?: number;
      limit?: number;
      employee_id?: string;
      statuses?: string;
      start_date?: string;
      end_date?: string;
      created_by?: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      // Build query string
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.employee_id)
        queryParams.append("employee_id", params.employee_id);
      if (params.statuses) queryParams.append("statuses", params.statuses);
      if (params.start_date)
        queryParams.append("start_date", params.start_date);
      if (params.end_date) queryParams.append("end_date", params.end_date);
      if (params.created_by)
        queryParams.append("created_by", params.created_by);

      const queryString = queryParams.toString();
      const url = `${API_URL}/schedule-logs/admin${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },

    listEmployee: async (params: {
      page?: number;
      limit?: number;
      employee_id: string;
      statuses?: string;
      start_date?: string;
      end_date?: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      // Build query string
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.employee_id)
        queryParams.append("employee_id", params.employee_id);
      if (params.statuses) queryParams.append("statuses", params.statuses);
      if (params.start_date)
        queryParams.append("start_date", params.start_date);
      if (params.end_date) queryParams.append("end_date", params.end_date);

      const queryString = queryParams.toString();
      const url = `${API_URL}/schedule-logs/employee${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },

    view: async (id: string) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/${id}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },

    approve: async (id: string, data: { admin_note?: string }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/${id}/approve`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    reject: async (id: string, data: { admin_note: string }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/${id}/reject`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    clockInAt: async (data: {
      employee_id: string;
      schedule_id: number;
      clock_in_at: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/clock-in/admin`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    clockIn: async (data: {
      employee_id: string;
      schedule_id: string;
      note?: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/clock-in`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    clockOut: async (data: {
      employee_id: string;
      schedule_log_id: string;
      note?: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/schedule-logs/clock-out`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(data),
      });

      return handleApiResponse(response);
    },

    getStats: async (params: {
      employee_id?: string;
      start_date?: string;
      end_date?: string;
      created_by?: string;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      // Build query string
      const queryParams = new URLSearchParams();
      if (params.employee_id)
        queryParams.append("employee_id", params.employee_id);
      if (params.start_date)
        queryParams.append("start_date", params.start_date);
      if (params.end_date) queryParams.append("end_date", params.end_date);
      if (params.created_by)
        queryParams.append("created_by", params.created_by);

      const queryString = queryParams.toString();
      const url = `${API_URL}/schedule-logs/stats${
        queryString ? `?${queryString}` : ""
      }`;

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },

    editTime: async (params: {
      id: number;
      clock_in_at?: string | null;
      clock_out_at?: string | null;
      break_start?: string | null;
      break_end?: string | null;
    }) => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }
      const response = await fetch(
        `${API_URL}/schedule-logs/${params.id}/edit-times`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          body: JSON.stringify(params),
        }
      );
      return handleApiResponse(response);
    },
  },
  timeSheets: {
    list: async (params: any = {}): Promise<any> => {
      const queryParams = new URLSearchParams();

      // Add all params to query string
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined && params[key] !== null) {
          queryParams.append(key, params[key]);
        }
      });

      const response = await fetch(
        `${API_URL}/time-sheets?${queryParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getAccessToken()}`,
          },
        }
      );

      return handleApiResponse(response);
    },

    view: async (id: number): Promise<any> => {
      const response = await fetch(`${API_URL}/time-sheets/${id}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${getAccessToken()}`,
        },
      });

      return handleApiResponse(response);
    },

    recordPayment: async (id: number, data: any): Promise<any> => {
      const response = await fetch(
        `${API_URL}/time-sheets/${id}/record-payment`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getAccessToken()}`,
          },
          body: JSON.stringify(data),
        }
      );

      return handleApiResponse(response);
    },

    getDailyAnalytics: async (id: number): Promise<any> => {
      const response = await fetch(
        `${API_URL}/time-sheets/${id}/daily-analytics`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getAccessToken()}`,
          },
        }
      );

      return handleApiResponse(response);
    },

    getStats: async (params: any = {}): Promise<any> => {
      const queryParams = new URLSearchParams();

      // Add all params to query string
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined && params[key] !== null) {
          queryParams.append(key, params[key]);
        }
      });

      const response = await fetch(
        `${API_URL}/time-sheets/stats?${queryParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getAccessToken()}`,
          },
        }
      );

      return handleApiResponse(response);
    },

    calculateTax: async (
      id: number,
      data: { percentage: number }
    ): Promise<any> => {
      const response = await fetch(
        `${API_URL}/time-sheets/${id}/calculate-tax`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getAccessToken()}`,
          },
          body: JSON.stringify(data),
        }
      );

      return handleApiResponse(response);
    },

    export: async (params: any = {}): Promise<Blob> => {
      const queryParams = new URLSearchParams();

      // Add all params to query string
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined && params[key] !== null) {
          queryParams.append(key, params[key]);
        }
      });

      const response = await fetch(
        `${API_URL}/time-sheets/export?${queryParams.toString()}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${getAccessToken()}`,
          },
        }
      );

      if (response.status === 401) {
        handleUnauthorizedError();
        throw new Error("Unauthorized - Please log in again");
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Export request failed");
      }

      return response.blob();
    },
  },
  creators: {
    list: async (): Promise<{ users: { id: string; email: string }[] }> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/companies/users`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
  },
  taxRates: {
    list: async (): Promise<{
      items: {
        id: number;
        company_id: number;
        percentage: number;
        created_at: string;
      }[];
    }> => {
      const accessToken = getAccessToken();
      if (!accessToken) {
        throw new Error("Not authenticated");
      }

      const response = await fetch(`${API_URL}/tax-rates`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });

      return handleApiResponse(response);
    },
  },
};
