"use client";

import { format, parseISO } from "date-fns";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import type { ScheduleEntry } from "./schedule-calendar";

interface DeleteScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entry: ScheduleEntry;
  employeeName: string;
  onConfirm: () => void;
  onSuccess?: () => void; // Add this prop
}

export function DeleteScheduleDialog({
  open,
  onOpenChange,
  entry,
  employeeName,
  onConfirm,
  onSuccess, // Add this prop
}: DeleteScheduleDialogProps) {
  const handleDelete = () => {
    onConfirm();

    // Call onSuccess to refetch the table data
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the scheduled shift for{" "}
            <strong>{employeeName}</strong> on{" "}
            <strong>{format(parseISO(entry.date), "PPPP")}</strong>. This action
            cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-red-600 hover:bg-red-700"
          >
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
