"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Clock } from "lucide-react";
import { api } from "@/lib/api";
import { errorParser } from "@/lib/error-parser";

interface ScheduleClockInDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleId: number | any;
  employeeData: {
    id: string;
    name: string;
  };
  onSuccess: () => void;
}

export function ScheduleClockInDialog({
  open,
  onOpenChange,
  scheduleId,
  employeeData,
  onSuccess,
}: ScheduleClockInDialogProps) {
  const [clockInTime, setClockInTime] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (open) {
      // Set default to current date/time
      const now = new Date();
      const pad = (n: number) => n.toString().padStart(2, "0");
      const defaultTime =
        now.getFullYear() +
        "-" +
        pad(now.getMonth() + 1) +
        "-" +
        pad(now.getDate()) +
        "T" +
        pad(now.getHours()) +
        ":" +
        pad(now.getMinutes());

      setClockInTime(defaultTime);
      setError("");
    }
  }, [open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setClockInTime(value);
  };

  // Helper to convert datetime-local to "YYYY-MM-DD hh:mm AM/PM" format
  function toAmPmFormat(val: string): string {
    if (!val) {
      throw new Error("Invalid datetime value");
    }

    const d = new Date(val);
    if (isNaN(d.getTime())) {
      throw new Error("Invalid datetime value");
    }

    let hours = d.getHours();
    const minutes = d.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    if (hours === 0) hours = 12;

    const pad = (n: number) => n.toString().padStart(2, "0");
    return (
      d.getFullYear() +
      "-" +
      pad(d.getMonth() + 1) +
      "-" +
      pad(d.getDate()) +
      " " +
      pad(hours) +
      ":" +
      pad(minutes) +
      " " +
      ampm
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!scheduleId || !clockInTime) return;

    setLoading(true);
    setError("");

    try {
      const formattedTime = toAmPmFormat(clockInTime);

      await api.scheduleLogs.clockInAt({
        schedule_id: Number(scheduleId),
        clock_in_at: formattedTime,
        employee_id: employeeData.id,
      });

      onSuccess();
      onOpenChange(false);
    } catch (err: any) {
      const error = errorParser(err);
      setError(error || "Failed to clock in");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Clock In
          </DialogTitle>
          <DialogDescription>
            Clock in for {employeeData?.name}'s scheduled shift.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="clock-in-time">Clock In Time</Label>
              <Input
                id="clock-in-time"
                name="clockInTime"
                type="datetime-local"
                value={clockInTime}
                onChange={handleChange}
                placeholder="Select clock in time"
                required
              />
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Clocking In...
                </>
              ) : (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  Clock In
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
