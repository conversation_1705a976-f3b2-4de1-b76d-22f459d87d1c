"use client";

import { useEffect, useState } from "react";
import { format, addDays, startOfMonth, endOfMonth } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import type { DateRange } from "react-day-picker";

import type { Employee } from "../employee/employee-list";
import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";
import type { ScheduleEntry } from "./schedule-calendar";
import { api } from "@/lib/api";
import { DatePickerWithRange } from "../ui/date-range-picker";

interface CreateScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (entry: Omit<ScheduleEntry, "id" | "createdAt">) => void;
  employees: Employee[];
  shifts: PayrollSetting[];
  selectedDate: Date | null;
  onSuccess?: () => void; // Add this prop
}

const formSchema = z.object({
  employeeId: z.string({
    required_error: "Please select an employee",
  }),
  dateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .refine((data) => data?.from !== undefined, {
      message: "Please select a start date",
      path: ["from"],
    }),
  startTime: z.string({
    required_error: "Please enter a start time",
  }),
  endTime: z.string({
    required_error: "Please enter an end time",
  }),
  notes: z.string().optional(),
});

function toAmPm(time: string) {
  if (!time) return "";
  let [hours, minutes] = time.split(":").map(Number);
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")} ${ampm}`;
}

export function CreateScheduleDialog({
  open,
  onOpenChange,
  onSubmit,
  employees: initialEmployees,
  shifts,
  selectedDate,
  onSuccess, // Add this prop
}: CreateScheduleDialogProps) {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: selectedDate || new Date(),
    to: addDays(selectedDate || new Date(), 6),
  });
  const [employees, setEmployees] = useState<Employee[]>(initialEmployees);
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch employees from API
    api.employees
      .list()
      .then((res) => setEmployees(res.employees))
      .catch(() => {});
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employeeId: "",
      dateRange: {
        from: selectedDate || new Date(),
        to: addDays(selectedDate || new Date(), 6),
      },
      startTime: "09:00",
      endTime: "17:00",
      notes: "",
    },
  });

  // Update form when selectedDate changes
  useEffect(() => {
    if (selectedDate) {
      form.setValue("dateRange", {
        from: selectedDate,
        to: addDays(selectedDate, 6),
      });
    }
  }, [selectedDate, form]);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      form.reset({
        employeeId: "",
        dateRange: {
          from: selectedDate || new Date(),
          to: addDays(selectedDate || new Date(), 6),
        },
        startTime: "09:00",
        endTime: "17:00",
        notes: "",
      });
      setDateRange({
        from: selectedDate || new Date(),
        to: addDays(selectedDate || new Date(), 6),
      });
    }
  }, [open, form, selectedDate]);

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!values.employeeId) {
      form.setError("employeeId", {
        type: "manual",
        message: "Please select an employee",
      });
      setLoading(false);
      return;
    }

    setApiError(null);
    setLoading(true);
    try {
      let start_date = "";
      let end_date = "";

      if (values.dateRange?.from) {
        start_date = format(values.dateRange.from, "yyyy-MM-dd");
        end_date = values.dateRange.to
          ? format(values.dateRange.to, "yyyy-MM-dd")
          : start_date;
      } else {
        throw new Error("Please select a valid date range");
      }

      const user = await api.auth.getCurrentUser();
      const payload = {
        company_id: Number(user.company_id),
        employee_id: values.employeeId,
        start_date,
        end_date,
        start_time: toAmPm(values.startTime),
        end_time: toAmPm(values.endTime),
        note: values.notes,
      };
      const created = await api.schedules.create(payload);
      onSubmit(created);
      onOpenChange(false);
      toast.success("Schedule created successfully");
      // Call onSuccess to refetch the table data
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      toast.error(err.message || "Failed to create schedule");
      setApiError(err.message || "Failed to create schedule");
    } finally {
      setLoading(false);
    }
  };

  const startTime = form.watch("startTime");
  const endTime = form.watch("endTime");

  // Calculate total hours based on schedule type
  const calculateTotalHours = () => {
    if (!startTime || !endTime)
      return { totalHours: 0, daysCount: 0, hoursPerDay: 0 };

    // Calculate hours per day
    const startParts = startTime.split(":").map(Number);
    const endParts = endTime.split(":").map(Number);

    const startHour = startParts[0] + startParts[1] / 60;
    let endHour = endParts[0] + endParts[1] / 60;

    // Handle overnight shifts
    if (endHour < startHour) {
      endHour += 24;
    }

    const hoursPerDay = endHour - startHour;

    // Calculate total days based on schedule type
    let daysCount = 1;
    const dateRangeValue = form.watch("dateRange");

    if (dateRangeValue?.from && dateRangeValue?.to) {
      // Calculate days between from and to dates
      const fromDate = dateRangeValue.from;
      const toDate = dateRangeValue.to || fromDate;
      daysCount =
        Math.round(
          (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
        ) + 1;
    }

    return {
      totalHours: hoursPerDay * daysCount,
      daysCount,
      hoursPerDay,
    };
  };

  const { totalHours, daysCount, hoursPerDay } = calculateTotalHours();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Schedule</DialogTitle>
          <DialogDescription>
            Assign an employee to a shift on a specific schedule.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employee</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an employee" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dateRange"
              render={({ field }) => (
                <FormItem className="flex flex-col w-full">
                  <FormLabel>Date Range</FormLabel>
                  <DatePickerWithRange
                    dateRange={field.value}
                    onDateRangeChange={(range) => {
                      field.onChange(range);
                      setDateRange(range);
                    }}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Time</FormLabel>
                    <FormControl>
                      <Input
                        type="time"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Time</FormLabel>
                    <FormControl>
                      <Input
                        type="time"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any notes about this schedule"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Schedule"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
