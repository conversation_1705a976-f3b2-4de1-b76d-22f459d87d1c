"use client";

import { useEffect, useState } from "react";
import { format, parseISO } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import type { DateRange } from "react-day-picker";

import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";
import type { ScheduleEntry } from "./schedule-calendar";
import { api } from "@/lib/api";
import { Employee } from "@/types/employee";

interface EditScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entry: ScheduleEntry;
  onSubmit: (entry: ScheduleEntry) => void;
  employees: any[];
  shifts: PayrollSetting[];
  onSuccess?: () => void;
}

const formSchema = z.object({
  employeeId: z.string({
    required_error: "Please select an employee",
  }),
  dateRange: z
    .object({
      from: z.date().optional(),
      to: z.date().optional(),
    })
    .refine((data) => data?.from !== undefined, {
      message: "Please select a start date",
      path: ["from"],
    }),
  startTime: z.string({
    required_error: "Please enter a start time",
  }),
  endTime: z.string({
    required_error: "Please enter an end time",
  }),
  notes: z.string().optional(),
});

function toAmPm(time: string) {
  if (!time) return "";
  let [hours, minutes] = time.split(":").map(Number);
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")} ${ampm}`;
}

function fromAmPm(timeStr: string) {
  if (!timeStr) return "";

  // Extract time and AM/PM
  const [time, period] = timeStr.split(" ");
  let [hours, minutes] = time.split(":").map(Number);

  // Convert to 24-hour format
  if (period === "PM" && hours < 12) hours += 12;
  if (period === "AM" && hours === 12) hours = 0;

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
}

export function EditScheduleDialog({
  open,
  onOpenChange,
  entry,
  onSubmit,
  employees,
  shifts,
  onSuccess,
}: EditScheduleDialogProps) {
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  // Initialize date range from entry
  const entryDate = parseISO(entry.date);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: entry.dateRange?.from ? parseISO(entry.dateRange.from) : entryDate,
    to: entry.dateRange?.to ? parseISO(entry.dateRange.to) : entryDate,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employeeId: entry.employeeId,
      dateRange: {
        from: entry.dateRange?.from
          ? parseISO(entry.dateRange.from)
          : entryDate,
        to: entry.dateRange?.to ? parseISO(entry.dateRange.to) : entryDate,
      },
      startTime: fromAmPm(entry.startTime) || "09:00",
      endTime: fromAmPm(entry.endTime) || "17:00",
      notes: entry.notes || "",
    },
  });

  // Update form when entry changes
  useEffect(() => {
    if (entry) {
      const entryDate = parseISO(entry.date);

      form.reset({
        employeeId: entry.employeeId,
        dateRange: {
          from: entry.dateRange?.from
            ? parseISO(entry.dateRange.from)
            : entryDate,
          to: entry.dateRange?.to ? parseISO(entry.dateRange.to) : entryDate,
        },
        startTime: fromAmPm(entry.startTime) || "09:00",
        endTime: fromAmPm(entry.endTime) || "17:00",
        notes: entry.notes || "",
      });

      setDateRange({
        from: entry.dateRange?.from
          ? parseISO(entry.dateRange.from)
          : entryDate,
        to: entry.dateRange?.to ? parseISO(entry.dateRange.to) : entryDate,
      });
    }
  }, [entry, form]);

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    setApiError(null);
    setLoading(true);
    try {
      let start_date = "";
      let end_date = "";

      if (values.dateRange?.from) {
        start_date = format(values.dateRange.from, "yyyy-MM-dd");
        end_date = values.dateRange.to
          ? format(values.dateRange.to, "yyyy-MM-dd")
          : start_date;
      } else {
        throw new Error("Please select a valid date range");
      }

      // Prepare payload for API - use toAmPm to convert time format
      const payload = {
        employee_id: values.employeeId,
        start_date,
        end_date,
        start_time: toAmPm(values.startTime),
        end_time: toAmPm(values.endTime),
        note: values.notes,
      };

      // Call API to update schedule
      await api.schedules.update(Number(entry.id), payload);

      // Update local state with the converted time format
      onSubmit({
        ...entry,
        employeeId: values.employeeId,
        date: start_date,
        startTime: toAmPm(values.startTime), // Use toAmPm here
        endTime: toAmPm(values.endTime), // Use toAmPm here
        notes: values.notes,
        dateRange: {
          from: start_date,
          to: end_date,
        },
      });

      onOpenChange(false);

      // Call onSuccess to refetch the table data
      if (onSuccess) {
        onSuccess();
      }
    } catch (err: any) {
      setApiError(err.message || "Failed to update schedule");
    } finally {
      setLoading(false);
    }
  };

  const startTime = form.watch("startTime");
  const endTime = form.watch("endTime");

  // Calculate total hours
  const calculateTotalHours = () => {
    if (!startTime || !endTime)
      return { totalHours: 0, daysCount: 0, hoursPerDay: 0 };

    // Calculate hours per day
    const startParts = startTime.split(":").map(Number);
    const endParts = endTime.split(":").map(Number);

    const startHour = startParts[0] + startParts[1] / 60;
    let endHour = endParts[0] + endParts[1] / 60;

    // Handle overnight shifts
    if (endHour < startHour) {
      endHour += 24;
    }

    const hoursPerDay = endHour - startHour;

    // Calculate total days based on date range
    let daysCount = 1;
    const dateRangeValue = form.watch("dateRange");

    if (dateRangeValue?.from && dateRangeValue?.to) {
      // Calculate days between from and to dates
      const fromDate = dateRangeValue.from;
      const toDate = dateRangeValue.to;
      daysCount =
        Math.round(
          (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
        ) + 1;
    }

    return {
      totalHours: hoursPerDay * daysCount,
      daysCount,
      hoursPerDay,
    };
  };

  const { totalHours, daysCount, hoursPerDay } = calculateTotalHours();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Schedule</DialogTitle>
          <DialogDescription>
            Update the employee schedule details.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Employee</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an employee" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dateRange"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Date</FormLabel>
                  <div className="relative w-full">
                    <style jsx global>{`
                      .react-datepicker-wrapper {
                        width: 100%;
                      }
                    `}</style>
                    <DatePicker
                      selected={field.value?.from}
                      onChange={(date) => {
                        if (date) {
                          field.onChange({
                            from: date,
                            to: date,
                          });
                          setDateRange({
                            from: date,
                            to: date,
                          });
                        }
                      }}
                      customInput={
                        <Button
                          type="button" // Add this to prevent form submission
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                          onClick={(e) => {
                            e.preventDefault(); // Add this to prevent form submission
                            e.stopPropagation(); // Add this to prevent event bubbling
                          }}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {field.value?.from ? (
                            format(field.value.from, "LLL dd, y")
                          ) : (
                            <span>Pick a date</span>
                          )}
                        </Button>
                      }
                      calendarClassName="bg-background border rounded-md shadow-md p-3"
                      dayClassName={(date) =>
                        cn(
                          "rounded-md hover:bg-muted",
                          date.toDateString() ===
                            field.value?.from?.toDateString() &&
                            "bg-primary text-primary-foreground"
                        )
                      }
                    />
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Time</FormLabel>
                    <FormControl>
                      <Input
                        type="time"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Time</FormLabel>
                    <FormControl>
                      <Input
                        type="time"
                        value={field.value}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {startTime && endTime && totalHours > 0 && (
              <div className="text-sm text-muted-foreground">
                <span className="font-medium">
                  Estimated total: {totalHours.toFixed(1)} hours
                </span>
                {daysCount > 1 && (
                  <span>
                    {" "}
                    ({hoursPerDay.toFixed(1)} hrs/day × {daysCount} days)
                  </span>
                )}
              </div>
            )}

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any notes about this schedule"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {apiError && (
              <div className="text-sm font-medium text-destructive">
                {apiError}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  "Save Changes"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
