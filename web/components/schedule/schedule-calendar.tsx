"use client";

import { useState, useEffect } from "react";
import {
  addDays,
  format,
  startOfWeek,
  addWeeks,
  isSameDay,
  parseISO,
  startOfMonth,
  endOfMonth,
  addMonths,
  isWithinInterval,
} from "date-fns";
import {
  Pencil,
  Trash2,
  Eye,
  Calendar,
  Clock,
  Plus,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { TopBar } from "@/components/top-bar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CreateScheduleDialog } from "./create-schedule-dialog";
import { EditScheduleDialog } from "./edit-schedule-dialog";
import { ViewScheduleDialog } from "./view-schedule-dialog";
import { DeleteScheduleDialog } from "./delete-schedule-dialog";
import type { Employee } from "../employee/employee-list";
import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";
import { api } from "@/lib/api";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { ScheduleClockInDialog } from "./schedule-clockin-modal";

// Sample payroll settings
const payrollSettings: PayrollSetting[] = [
  {
    id: "1",
    name: "Morning Shift",
    startTime: "06:00",
    endTime: "14:00",
    breakTime: 60,
    hourlyRate: 18,
    createdAt: "2025-04-15T10:30:00Z",
  },
  {
    id: "2",
    name: "Evening Shift",
    startTime: "14:00",
    endTime: "22:00",
    breakTime: 30,
    hourlyRate: 22,
    createdAt: "2025-04-15T11:45:00Z",
  },
  {
    id: "3",
    name: "Night Shift",
    startTime: "22:00",
    endTime: "06:00",
    breakTime: 45,
    hourlyRate: 25,
    createdAt: "2025-04-16T09:15:00Z",
  },
];

// Schedule entry type - updated to include direct time fields
export type ScheduleEntry = {
  id: string;
  employeeId: string;
  date: string;
  startTime: string;
  endTime: string;
  breakTime?: string;
  notes?: string;
  createdAt: string;
  scheduleType?: "day" | "weekly" | "monthly";
  dateRange?: {
    from: string;
    to?: string;
  };
  month?: string;
  status?: "assigned" | "ongoing" | "completed";
};

// Add this type definition for status colors
const statusColors = {
  assigned: "default",
  ongoing: "warning",
  completed: "success",
} as const;

export function ScheduleCalendar() {
  const [scheduleEntries, setScheduleEntries] = useState<ScheduleEntry[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"week" | "month">("week");
  const [selectedEmployee, setSelectedEmployee] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Changed from 5 to 10 to show 10 rows at a time

  // Add state for employees from API
  const [employees, setEmployees] = useState<Employee[]>([]);

  // Add state for creators and selected creator
  const [creators, setCreators] = useState<
    {
      id: string;
      email: string;
      raw_user_meta_data: { display_name: string };
    }[]
  >([]);
  const [selectedCreator, setSelectedCreator] = useState<string>("all");

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState<ScheduleEntry | null>(
    null
  );
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [schedules, setSchedules] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  //clock in add states
  const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(
    null
  );
  const [clockInEmployee, setClockInEmployee] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [isClockInDialogOpen, setIsClockInDialogOpen] = useState(false);

  // Handle clock in click
  const handleClockInClick = (schedule: any) => {
    setSelectedScheduleId(schedule.id.toString());
    setClockInEmployee({
      id: schedule.employee_id,
      name: schedule.employee?.name ?? "",
    });
    setIsClockInDialogOpen(true);
  };

  // Add a function to fetch schedules
  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const params: any = {};

      // Add employee filter if selected
      if (selectedEmployee !== "all") {
        params.employee_id = selectedEmployee;
      }
      if (selectedCreator !== "all") {
        params.created_by = selectedCreator;
      }

      const response = await api.schedules.listSchedules(params);
      setSchedules(response.schedules);
    } catch (err) {
      setError("Failed to fetch schedules");
    } finally {
      setLoading(false);
    }
  };

  // Fetch employees from API
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        const response = await api.employees.list();
        setEmployees(response.employees || []);
      } catch (err) {
        console.error("Failed to fetch employees:", err);
      }
    };
    fetchEmployees();
  }, []);

  // Fetch creators from API
  useEffect(() => {
    const fetchCreators = async () => {
      try {
        const response = await api.creators.list();
        setCreators(
          (response?.users || []).map((user: any) => ({
            id: user.id,
            email: user.email,
            raw_user_meta_data: user.raw_user_meta_data || {
              display_name: null,
            },
          }))
        );
      } catch (err) {
        console.error("Failed to fetch creators:", err);
      }
    };
    fetchCreators();
  }, []);

  // Call this function in the initial useEffect
  useEffect(() => {
    fetchSchedules();
  }, [selectedEmployee, selectedCreator]); // Add selectedEmployee and selectedCreator as dependencies

  // Generate days for week view
  const generateWeekDays = (startDate: Date) => {
    const days = [];
    const weekStart = startOfWeek(startDate);
    for (let i = 0; i < 7; i++) {
      days.push(addDays(weekStart, i));
    }
    return days;
  };

  // Generate days for month view
  const generateMonthDays = (date: Date) => {
    const start = startOfMonth(date);
    const end = endOfMonth(date);
    const days = [];

    // Start from the beginning of the week containing the first day of the month
    let currentDay = startOfWeek(start);

    // Generate days until we reach the end of the month
    while (currentDay <= end) {
      days.push(currentDay);
      currentDay = addDays(currentDay, 1);
    }

    return days;
  };

  // Get days based on current view mode
  const days =
    viewMode === "week"
      ? generateWeekDays(currentDate)
      : generateMonthDays(currentDate);

  // Filter schedule entries based on selected employee and date range
  const filteredEntries = scheduleEntries.filter((entry) => {
    if (!entry.date) return false;
    const entryDate = parseISO(entry.date);
    const isInRange = days.some((day) => isSameDay(entryDate, day));
    return (
      (selectedEmployee === "all" || entry.employeeId === selectedEmployee) &&
      isInRange
    );
  });

  // Get entries for a specific day
  const getEntriesForDay = (day: Date) => {
    return filteredEntries.filter((entry) =>
      isSameDay(parseISO(entry.date), day)
    );
  };

  // Get employee name by ID
  const getEmployeeName = (id: string) => {
    const employee = employees.find((emp) => emp.id === id);
    return employee ? employee.name : "Unknown Employee";
  };

  // Handle navigation
  const navigatePrevious = () => {
    if (viewMode === "week") {
      setCurrentDate(addWeeks(currentDate, -1));
    } else {
      setCurrentDate(addMonths(currentDate, -1));
    }
  };

  const navigateNext = () => {
    if (viewMode === "week") {
      setCurrentDate(addWeeks(currentDate, 1));
    } else {
      setCurrentDate(addMonths(currentDate, 1));
    }
  };

  // Handle create schedule entry
  const handleCreateEntry = (
    newEntry: Omit<ScheduleEntry, "id" | "createdAt">
  ) => {
    const entry: ScheduleEntry = {
      id: (scheduleEntries.length + 1).toString(),
      ...newEntry,
      createdAt: new Date().toISOString(),
    };
    setScheduleEntries([...scheduleEntries, entry]);
    setIsCreateDialogOpen(false);
  };

  // Handle edit schedule entry
  const handleEditEntry = async (updatedEntry: ScheduleEntry) => {
    try {
      // The API call is already made in the EditScheduleDialog component
      // We just need to update the local state here
      setScheduleEntries(
        scheduleEntries.map((entry) =>
          entry.id === updatedEntry.id ? updatedEntry : entry
        )
      );
      setIsEditDialogOpen(false);

      // Refetch schedules to ensure data consistency
      fetchSchedules();
    } catch (error) {
      console.error("Failed to update schedule:", error);
    }
  };

  // Handle delete schedule entry
  const handleDeleteEntry = async (id: string) => {
    try {
      // Call API to delete schedule
      await api.schedules.delete(Number(id));

      // Update local state
      setScheduleEntries(scheduleEntries.filter((entry) => entry.id !== id));
      setIsDeleteDialogOpen(false);

      // Refetch schedules
      fetchSchedules();
    } catch (error) {
      console.error("Failed to delete schedule:", error);
    }
  };

  // Handle day click to add new schedule
  const handleDayClick = (day: Date) => {
    setSelectedDate(day);
    setIsCreateDialogOpen(true);
  };

  // Handle entry click
  const handleEntryClick = (entry: ScheduleEntry) => {
    setSelectedEntry(entry);
    setIsViewDialogOpen(true);
  };

  // Calculate pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = schedules.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(schedules.length / itemsPerPage);

  // Handle page changes
  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Add this function to convert from AM/PM format to 24-hour format
  function fromAmPm(timeStr: string) {
    if (!timeStr) return "";

    // Extract time and AM/PM
    const [time, period] = timeStr.split(" ");
    let [hours, minutes] = time.split(":").map(Number);

    // Convert to 24-hour format
    if (period === "PM" && hours < 12) hours += 12;
    if (period === "AM" && hours === 12) hours = 0;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;
  }

  return (
    <>
      <DashboardSidebar />
      <div className="flex-1 overflow-auto h-screen">
        <TopBar title="Schedule Management" />
        <main className="p-6 pb-24">
          <div className="flex flex-col gap-6">
            {/* Controls */}
            {/* <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"> */}
            {/* <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={navigatePrevious}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-lg font-medium">
                  {viewMode === "week" ? `Week of ${format(days[0], "MMM d, yyyy")}` : format(currentDate, "MMMM yyyy")}
                </div>
                <Button variant="outline" size="icon" onClick={navigateNext}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div> */}
            <div className="flex flex-wrap items-center justify-end gap-2">
              {/* <Tabs defaultValue="week" onValueChange={(value) => setViewMode(value as "week" | "month")}>
                  <TabsList>
                    <TabsTrigger value="week">Week</TabsTrigger>
                    <TabsTrigger value="month">Month</TabsTrigger>
                  </TabsList>
                </Tabs> */}
              <Select
                value={selectedEmployee}
                onValueChange={setSelectedEmployee}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select employee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Employees</SelectItem>
                  {employees.map((employee) => (
                    <SelectItem key={employee.id} value={employee.id}>
                      {employee.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Creator Filter */}
              <Select
                value={selectedCreator}
                onValueChange={setSelectedCreator}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select creator" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Assignees</SelectItem>
                  {creators.map((creator) => (
                    <SelectItem key={creator.id} value={creator.id}>
                      {creator.raw_user_meta_data?.display_name ||
                        creator.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                onClick={() => {
                  setSelectedDate(new Date());
                  setIsCreateDialogOpen(true);
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Schedule
              </Button>
            </div>
            {/* </div> */}

            {/* Calendar */}
            {/* {viewMode === "week" ? (
              <div className="grid grid-cols-7 gap-4">
                {days.map((day, index) => (
                  <div key={index} className="flex flex-col">
                    <div className="text-center p-2 bg-muted rounded-t-md">
                      <div className="font-medium">{format(day, "EEE")}</div>
                      <div
                        className={`text-sm ${isSameDay(day, new Date()) ? "bg-primary text-primary-foreground rounded-full w-7 h-7 flex items-center justify-center mx-auto" : ""}`}
                      >
                        {format(day, "d")}
                      </div>
                    </div>
                    <Card className="flex-1 min-h-[200px] rounded-t-none p-1">
                      <div
                        className="h-full w-full p-1 cursor-pointer hover:bg-muted/50 rounded-sm"
                        onClick={() => handleDayClick(day)}
                      >
                        {getEntriesForDay(day).map((entry) => (
                          <div
                            key={entry.id}
                            className="mb-1 p-2 text-xs bg-primary/10 rounded-md cursor-pointer hover:bg-primary/20"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEntryClick(entry)
                            }}
                          >
                            <div className="font-medium">{getEmployeeName(entry.employeeId)}</div>
                            <div className="flex items-center gap-1 text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              {entry.startTime} - {entry.endTime}
                            </div>
                            {entry.scheduleType && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {entry.scheduleType === "day"
                                  ? "Single Day"
                                  : entry.scheduleType === "weekly"
                                    ? "Weekly Schedule"
                                    : "Monthly Schedule"}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </Card>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-7 gap-1">
                {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                  <div key={day} className="text-center p-2 font-medium">
                    {day}
                  </div>
                ))}
                {days.map((day, index) => {
                  const isCurrentMonth = isWithinInterval(day, {
                    start: startOfMonth(currentDate),
                    end: endOfMonth(currentDate),
                  })

                  return (
                    <div
                      key={index}
                      className={`min-h-[100px] border p-1 ${isCurrentMonth ? "" : "bg-muted/30"}`}
                      onClick={() => handleDayClick(day)}
                    >
                      <div
                        className={`text-right mb-1 ${isSameDay(day, new Date()) ? "bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center ml-auto" : ""}`}
                      >
                        {format(day, "d")}
                      </div>
                      <div className="overflow-y-auto max-h-[80px]">
                        {getEntriesForDay(day).map((entry) => (
                          <div
                            key={entry.id}
                            className="mb-1 p-1 text-xs bg-primary/10 rounded-md cursor-pointer hover:bg-primary/20"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleEntryClick(entry)
                            }}
                          >
                            <div className="font-medium truncate">{getEmployeeName(entry.employeeId)}</div>
                            <div className="flex items-center gap-1 text-muted-foreground truncate">
                              <Calendar className="h-3 w-3 shrink-0" />
                              {entry.startTime} - {entry.endTime}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            )} */}

            {/* Schedule Summary */}
            <div className="mt-4">
              <h3 className="text-lg font-semibold mb-4">Upcoming Shifts</h3>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employee</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Notes</TableHead>
                      <TableHead>Created By</TableHead>{" "}
                      {/* <-- Add this line */}
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="text-center py-8 text-muted-foreground"
                        >
                          Loading...
                        </TableCell>
                      </TableRow>
                    ) : error ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="text-center py-8 text-red-500"
                        >
                          {error}
                        </TableCell>
                      </TableRow>
                    ) : currentItems.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={5}
                          className="text-center py-8 text-muted-foreground"
                        >
                          No scheduled shifts found
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentItems.map((schedule) => (
                        <TableRow key={schedule.id}>
                          <TableCell className="font-medium">
                            {schedule.employee?.name ||
                              getEmployeeName(schedule.employee_id)}
                          </TableCell>
                          <TableCell>
                            {format(
                              new Date(schedule.start_date),
                              "MMM d, yyyy"
                            )}
                          </TableCell>
                          <TableCell>{`${schedule.start_time} - ${schedule.end_time}`}</TableCell>
                          <TableCell>{schedule.note || "-"}</TableCell>
                          <TableCell>
                            {schedule.user?.raw_user_meta_data?.display_name ||
                              schedule.user?.email ||
                              "-"}
                          </TableCell>{" "}
                          {/* <-- Add this cell */}
                          <TableCell>
                            <Badge
                              className="capitalize"
                              variant={
                                schedule.status === "ongoing"
                                  ? "ongoing"
                                  : schedule.status === "completed"
                                  ? "completed"
                                  : "assigned"
                              }
                            >
                              {schedule.status || "Assigned"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-5">
                              {(schedule.status === "assigned" ||
                                !schedule.status) && (
                                <>
                                  <Button
                                    variant="ghost"
                                    className="h-4 w-4"
                                    size="none"
                                    onClick={() => handleClockInClick(schedule)}
                                    title="Clock In"
                                  >
                                    <Clock className="h-4 w-4" />
                                    <span className="sr-only">Clock In</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    className="h-4 w-4"
                                    size="none"
                                    onClick={() => {
                                      const entry: ScheduleEntry = {
                                        id: schedule.id.toString(),
                                        employeeId:
                                          schedule.employee_id.toString(),
                                        date: format(
                                          new Date(schedule.start_date),
                                          "yyyy-MM-dd"
                                        ),
                                        startTime: schedule.start_time,
                                        endTime: schedule.end_time,
                                        breakTime:
                                          schedule.break_time?.toString() ||
                                          "0",
                                        createdAt:
                                          schedule.created_at ||
                                          new Date().toISOString(),
                                        notes: schedule.note || "",
                                        dateRange: {
                                          from: format(
                                            new Date(schedule.start_date),
                                            "yyyy-MM-dd"
                                          ),
                                          to: format(
                                            new Date(schedule.end_date),
                                            "yyyy-MM-dd"
                                          ),
                                        },
                                      };
                                      setSelectedEntry(entry);
                                      setIsEditDialogOpen(true);
                                    }}
                                  >
                                    <Pencil className="h-4 w-4" />
                                    <span className="sr-only">Edit</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    className="h-4 w-4"
                                    size="none"
                                    onClick={() => {
                                      const entry: ScheduleEntry = {
                                        id: schedule.id.toString(),
                                        employeeId:
                                          schedule.employee_id.toString(),
                                        date: format(
                                          new Date(schedule.start_date),
                                          "yyyy-MM-dd"
                                        ),
                                        startTime: schedule.start_time,
                                        endTime: schedule.end_time,
                                        breakTime:
                                          schedule.break_time?.toString() ||
                                          "0",
                                        createdAt:
                                          schedule.created_at ||
                                          new Date().toISOString(),
                                        notes: schedule.note || "",
                                        dateRange: {
                                          from: format(
                                            new Date(schedule.start_date),
                                            "yyyy-MM-dd"
                                          ),
                                          to: format(
                                            new Date(schedule.end_date),
                                            "yyyy-MM-dd"
                                          ),
                                        },
                                      };
                                      setSelectedEntry(entry);
                                      setIsDeleteDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    <span className="sr-only">Delete</span>
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {!loading && !error && schedules.length > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {indexOfFirstItem + 1}-
                    {Math.min(indexOfLastItem, schedules.length)} of{" "}
                    {schedules.length} shifts
                  </div>
                  <Pagination className="justify-end">
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={handlePrevPage}
                          className={
                            currentPage === 1
                              ? "pointer-events-none opacity-50"
                              : "cursor-pointer"
                          }
                        />
                      </PaginationItem>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={page === currentPage}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      )}
                      <PaginationItem>
                        <PaginationNext
                          onClick={handleNextPage}
                          className={
                            currentPage === totalPages
                              ? "pointer-events-none opacity-50"
                              : "cursor-pointer"
                          }
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </div>
          </div>

          {/* Create Schedule Dialog */}
          <CreateScheduleDialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
            onSubmit={handleCreateEntry}
            employees={employees}
            shifts={payrollSettings}
            selectedDate={selectedDate}
            onSuccess={fetchSchedules}
          />

          {/* Clock In Dialog */}
          {clockInEmployee && (
            <ScheduleClockInDialog
              open={isClockInDialogOpen}
              onOpenChange={setIsClockInDialogOpen}
              scheduleId={selectedScheduleId}
              employeeData={clockInEmployee}
              onSuccess={fetchSchedules}
            />
          )}

          {/* Edit Schedule Dialog */}
          {selectedEntry && (
            <EditScheduleDialog
              open={isEditDialogOpen}
              onOpenChange={setIsEditDialogOpen}
              entry={selectedEntry}
              onSubmit={handleEditEntry}
              employees={employees}
              shifts={payrollSettings}
              onSuccess={fetchSchedules}
            />
          )}

          {/* View Schedule Dialog */}
          {selectedEntry && (
            <ViewScheduleDialog
              open={isViewDialogOpen}
              onOpenChange={setIsViewDialogOpen}
              entry={selectedEntry}
              employees={employees}
              shifts={payrollSettings}
              onEdit={() => {
                setIsViewDialogOpen(false);
                setIsEditDialogOpen(true);
              }}
              onDelete={() => {
                setIsViewDialogOpen(false);
                setIsDeleteDialogOpen(true);
              }}
            />
          )}

          {/* Delete Schedule Dialog */}
          {selectedEntry && (
            <DeleteScheduleDialog
              open={isDeleteDialogOpen}
              onOpenChange={setIsDeleteDialogOpen}
              entry={selectedEntry}
              employeeName={getEmployeeName(selectedEntry.employeeId)}
              onConfirm={() => handleDeleteEntry(selectedEntry.id)}
              onSuccess={fetchSchedules}
            />
          )}
        </main>
      </div>
    </>
  );
}
