"use client";

import { format, parseISO } from "date-fns";
import { Calendar, Clock } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";
import type { ScheduleEntry } from "./schedule-calendar";

interface ViewScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  entry: ScheduleEntry;
  employees: any[];
  shifts: PayrollSetting[];
  onEdit: () => void;
  onDelete: () => void;
}

export function ViewScheduleDialog({
  open,
  onOpenChange,
  entry,
  employees,
  shifts,
  onEdit,
  onDelete,
}: ViewScheduleDialogProps) {
  // Find employee details
  const employee = employees.find((emp) => emp.id === entry.employeeId);

  // Calculate hours
  const calculateHours = () => {
    if (!entry.startTime || !entry.endTime)
      return { hoursPerDay: 0, totalHours: 0, daysCount: 0 };

    // Calculate hours per day
    const startParts = entry.startTime.split(":").map(Number);
    const endParts = entry.endTime.split(":").map(Number);

    const startHour = startParts[0] + startParts[1] / 60;
    let endHour = endParts[0] + endParts[1] / 60;

    // Handle overnight shifts
    if (endHour < startHour) {
      endHour += 24;
    }

    const breakTimeValue = Number.parseFloat(entry.breakTime || "0") || 0;
    const hoursPerDay = endHour - startHour - breakTimeValue / 60;

    // Calculate total days based on schedule type
    let daysCount = 1;

    if (entry.scheduleType === "weekly" && entry.dateRange) {
      // If we have a date range, calculate days between
      if (entry.dateRange.from && entry.dateRange.to) {
        const fromDate = parseISO(entry.dateRange.from);
        const toDate = parseISO(entry.dateRange.to);
        daysCount =
          Math.round(
            (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
          ) + 1;
      } else if (entry.dateRange.from) {
        // If only from date, assume it's a week
        daysCount = 7;
      }
    } else if (entry.scheduleType === "monthly") {
      // For monthly, estimate as 30 days
      daysCount = 30;
    }

    const totalHours = hoursPerDay * daysCount;
    return { hoursPerDay, totalHours, daysCount };
  };

  const { hoursPerDay, totalHours, daysCount } = calculateHours();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Schedule Details</DialogTitle>
          <DialogDescription>
            View details for this scheduled shift.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 space-y-4">
          <div className="grid grid-cols-[120px_1fr] gap-2">
            <div className="font-medium text-muted-foreground">Employee:</div>
            <div>{employee?.name || "Unknown Employee"}</div>

            <div className="font-medium text-muted-foreground">Date:</div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              {format(parseISO(entry.date), "PPPP")}
            </div>

            <div className="font-medium text-muted-foreground">Time:</div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>
                {entry.startTime} - {entry.endTime}
              </span>
            </div>

            <div className="font-medium text-muted-foreground">Break Time:</div>
            <div>{entry.breakTime || "0"} minutes</div>

            <div className="font-medium text-muted-foreground">
              Daily Hours:
            </div>
            <div>{hoursPerDay.toFixed(1)} hours</div>

            {entry.scheduleType !== "day" && (
              <>
                <div className="font-medium text-muted-foreground">
                  Total Hours:
                </div>
                <div>
                  {totalHours.toFixed(1)} hours ({daysCount} days)
                </div>
              </>
            )}

            <div className="font-medium text-muted-foreground">
              Schedule Type:
            </div>
            <div>
              {entry.scheduleType === "day"
                ? "Single Day"
                : entry.scheduleType === "weekly"
                ? "Weekly"
                : entry.scheduleType === "monthly"
                ? "Monthly"
                : "Single Day"}
            </div>

            {entry.scheduleType === "weekly" && entry.dateRange && (
              <>
                <div className="font-medium text-muted-foreground">
                  Date Range:
                </div>
                <div>
                  {entry.dateRange.from &&
                    format(parseISO(entry.dateRange.from), "MMM d, yyyy")}
                  {entry.dateRange.to &&
                    ` - ${format(parseISO(entry.dateRange.to), "MMM d, yyyy")}`}
                </div>
              </>
            )}

            {entry.scheduleType === "monthly" && entry.month && (
              <>
                <div className="font-medium text-muted-foreground">Month:</div>
                <div>{format(parseISO(entry.month + "-01"), "MMMM yyyy")}</div>
              </>
            )}

            {entry.notes && (
              <>
                <div className="font-medium text-muted-foreground">Notes:</div>
                <div>{entry.notes}</div>
              </>
            )}

            <div className="font-medium text-muted-foreground">Created:</div>
            <div>{format(parseISO(entry.createdAt), "PPp")}</div>
          </div>
        </div>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button variant="secondary" onClick={onEdit}>
            Edit
          </Button>
          <Button variant="destructive" onClick={onDelete}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
