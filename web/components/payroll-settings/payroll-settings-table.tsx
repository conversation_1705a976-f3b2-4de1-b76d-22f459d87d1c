"use client";

import { <PERSON>, <PERSON>ci<PERSON>, Trash2, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { PayrollSetting } from "./payroll-settings-list";

interface PayrollSettingsTableProps {
  settings: PayrollSetting[];
  onEdit: (setting: PayrollSetting) => void;
  onDelete: (setting: PayrollSetting) => void;
}

export function PayrollSettingsTable({
  settings,
  onEdit,
  onDelete,
}: PayrollSettingsTableProps) {
  // Format time to 12-hour format
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(":");
    const hour = Number.parseInt(hours, 10);
    const ampm = hour >= 12 ? "PM" : "AM";
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minutes} ${ampm}`;
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Time Period</TableHead>
            <TableHead>Break Time</TableHead>
            <TableHead>Rate</TableHead>
            <TableHead>Hourly Rate</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {settings.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={7}
                className="text-center py-8 text-muted-foreground"
              >
                No payroll settings found
              </TableCell>
            </TableRow>
          ) : (
            settings.map((setting) => (
              <TableRow key={setting.id}>
                <TableCell className="font-medium">
                  <div className="space-y-1">
                    <div>{setting.name}</div>
                    {setting.is_base_price && (
                      <div className="flex items-center gap-1 text-xs text-green-600 bg-green-50 px-2 py-1 rounded-md w-fit">
                        <Star className="h-3 w-3 fill-current" />
                        Base Price
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {setting.startTime} - {setting.endTime}
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Coffee className="h-4 w-4 mr-1 text-muted-foreground" />
                    {setting.breakTime} min
                  </div>
                </TableCell>
                <TableCell>SEK {setting.rate.toFixed(2)}/hr</TableCell>
                <TableCell>SEK {setting.hourlyRate.toFixed(2)}/hr</TableCell>
                <TableCell>
                  {new Date(setting.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(setting)}
                    >
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>
                    {/* <Button variant="ghost" size="icon" onClick={() => onDelete(setting)}>
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button> */}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
