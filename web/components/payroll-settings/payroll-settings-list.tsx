"use client";

import { useState, useEffect } from "react";
import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Search } from "lucide-react";
import { PayrollSettingsTable } from "./payroll-settings-table";
import { CreatePayrollSettingDialog } from "./create-payroll-setting-dialog";
import { EditPayrollSettingDialog } from "./edit-payroll-setting-dialog";
import { DeletePayrollSettingDialog } from "./delete-payroll-setting-dialog";
import { TopBar } from "@/components/top-bar";
import { api } from "@/lib/api";
import { toast } from "sonner";

export type PayrollSetting = {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  breakTime: number; // Break time in minutesz
  rate: number;
  hourlyRate: number;
  createdAt: string;
  is_base_price?: boolean; // Indicates if this is a base price setting
};

export function PayrollSettingsList() {
  const [payrollSettings, setPayrollSettings] = useState<PayrollSetting[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState<PayrollSetting | null>(
    null
  );

  // Fetch payroll settings from API
  const fetchPayrollSettings = async () => {
    try {
      const data = await api.timeSlots.list();
      // Map API response to PayrollSetting[]
      const slots = data.time_slots || data.timeSlots || data;
      const mapped = slots.map((slot: any) => ({
        id: slot.id,
        name: slot.name,
        startTime: slot.start_time,
        endTime: slot.end_time,
        breakTime: slot.break_time,
        rate: parseFloat(slot.rate), // Use hourly_rate from API for rate
        hourlyRate: parseFloat(slot.hourly_rate), // Use hourly_rate from API for rate
        createdAt: slot.created_at,
        is_base_price: slot.is_base_price || false,
      }));
      setPayrollSettings(mapped);
    } catch (err) {
      toast.error("Failed to fetch payroll settings");
      setPayrollSettings([]);
    }
  };

  useEffect(() => {
    fetchPayrollSettings();
  }, []);

  // Filter payroll settings based on search query
  const filteredSettings = payrollSettings.filter(
    (setting) =>
      setting.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `$${setting.rate}`.includes(searchQuery)
  );

  // Handle create payroll setting
  const handleCreateSetting = async () => {
    setIsCreateDialogOpen(false);
    await fetchPayrollSettings(); // Refetch data from API
    toast.success("Payroll setting created successfully");
  };

  // Handle edit payroll setting
  const handleEditSetting = async () => {
    setIsEditDialogOpen(false);
    await fetchPayrollSettings(); // Refetch data from API
    toast.success("Payroll setting updated successfully");
  };

  // Handle delete payroll setting
  const handleDeleteSetting = async () => {
    setIsDeleteDialogOpen(false);
    await fetchPayrollSettings(); // Refetch data from API
    toast.success("Payroll setting deleted successfully");
  };

  // Handle edit button click
  const handleEditClick = (setting: PayrollSetting) => {
    setSelectedSetting(setting);
    setIsEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (setting: PayrollSetting) => {
    setSelectedSetting(setting);
    setIsDeleteDialogOpen(true);
  };

  return (
    <>
      <DashboardSidebar />
      <div className="flex-1 overflow-auto h-screen">
        <TopBar title="Payroll Settings" />
        <main className="p-6 pb-24">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search settings..."
                className="w-full pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Setting
            </Button>
          </div>

          <PayrollSettingsTable
            settings={filteredSettings}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
          />

          {/* Create Payroll Setting Dialog */}
          <CreatePayrollSettingDialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
            onSubmit={handleCreateSetting}
          />

          {/* Edit Payroll Setting Dialog */}
          {selectedSetting && (
            <EditPayrollSettingDialog
              open={isEditDialogOpen}
              onOpenChange={setIsEditDialogOpen}
              setting={selectedSetting}
              onSubmit={handleEditSetting}
            />
          )}

          {/* Delete Payroll Setting Dialog */}
          {selectedSetting && (
            <DeletePayrollSettingDialog
              open={isDeleteDialogOpen}
              onOpenChange={setIsDeleteDialogOpen}
              setting={selectedSetting}
              onConfirm={handleDeleteSetting}
            />
          )}
        </main>
      </div>
    </>
  );
}
