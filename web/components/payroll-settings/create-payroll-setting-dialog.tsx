"use client";

import type React from "react";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import type { PayrollSetting } from "./payroll-settings-list";
import { api } from "@/lib/api";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

interface CreatePayrollSettingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: () => void;
}

function toAmPm(str: string) {
  // Converts 'HH:mm' to 'hh:mm AM/PM'
  if (!str) return "";
  let [hours, minutes] = str.split(":").map(Number);
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")} ${ampm}`;
}

export function CreatePayrollSettingDialog({
  open,
  onOpenChange,
  onSubmit,
}: CreatePayrollSettingDialogProps) {
  const initialFormData = {
    name: "",
    startTime: "09:00",
    endTime: "17:00",
    breakTime: 30,
    rate: 20,
    is_base_price: false,
  };

  const initialErrors = {
    name: "",
    startTime: "",
    endTime: "",
    breakTime: "",
    rate: "",
    api: "",
  };
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState(initialErrors);
  const [loading, setLoading] = useState(false);

  // Reset form when dialog is closed
  useEffect(() => {
    if (!open) {
      setFormData(initialFormData);
      setErrors(initialErrors);
      setLoading(false);
    }
  }, [open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    if (name === "rate" || name === "breakTime") {
      const numValue = Number.parseFloat(value);
      setFormData((prev) => ({
        ...prev,
        [name]: isNaN(numValue) ? 0 : numValue,
      }));
    } else if (type === "checkbox") {
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    setErrors((prev) => ({ ...prev, [name]: "", api: "" }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      startTime: "",
      endTime: "",
      breakTime: "",
      rate: "",
      api: "",
    };
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      isValid = false;
    }
    if (!formData.startTime) {
      newErrors.startTime = "Start time is required";
      isValid = false;
    }
    if (!formData.endTime) {
      newErrors.endTime = "End time is required";
      isValid = false;
    }
    if (formData.rate <= 0) {
      newErrors.rate = "Rate must be greater than 0";
      isValid = false;
    }
    if (formData.breakTime < 0) {
      newErrors.breakTime = "Break time cannot be negative";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;
    setLoading(true);
    try {
      // Get company_id from current user
      const user = await api.auth.getCurrentUser();
      const created = await api.timeSlots.create({
        company_id: user.company_id,
        name: formData.name,
        start_time: toAmPm(formData.startTime),
        end_time: toAmPm(formData.endTime),
        break_time: formData.breakTime,
        rate: formData.rate,
        is_base_price: formData.is_base_price,
      });
      setFormData(initialFormData);
      onOpenChange(false);
      onSubmit();
    } catch (err: any) {
      setErrors((prev) => ({
        ...prev,
        api: err.message || "Failed to create time slot",
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Payroll Setting</DialogTitle>
          <DialogDescription>
            Create a new time-based payroll rate setting.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Setting Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Morning Shift"
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="startTime">Start Time</Label>
                  <Input
                    id="startTime"
                    name="startTime"
                    type="time"
                    value={formData.startTime}
                    onChange={handleChange}
                  />
                  {errors.startTime && (
                    <p className="text-sm text-red-500">{errors.startTime}</p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="endTime">End Time</Label>
                  <Input
                    id="endTime"
                    name="endTime"
                    type="time"
                    value={formData.endTime}
                    onChange={handleChange}
                  />
                  {errors.endTime && (
                    <p className="text-sm text-red-500">{errors.endTime}</p>
                  )}
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="breakTime">Break Time (minutes)</Label>
                <Input
                  id="breakTime"
                  name="breakTime"
                  type="number"
                  min="0"
                  step="5"
                  value={formData.breakTime}
                  onChange={handleChange}
                />
                {errors.breakTime && (
                  <p className="text-sm text-red-500">{errors.breakTime}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="rate">Rate (SEK)</Label>
                  <Input
                    id="rate"
                    name="rate"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.rate}
                    onChange={handleChange}
                  />
                  {errors.rate && (
                    <p className="text-sm text-red-500">{errors.rate}</p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="is_base_price">Base Price Setting</Label>
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="is_base_price"
                      name="is_base_price"
                      checked={formData.is_base_price}
                      onCheckedChange={(checked) =>
                        setFormData((prev) => ({
                          ...prev,
                          is_base_price: checked === true,
                        }))
                      }
                    />
                    <Label
                      htmlFor="is_base_price"
                      className="text-sm font-normal"
                    >
                      Mark as base price
                    </Label>
                  </div>
                </div>
              </div>
              {errors.api && (
                <p className="text-sm text-red-500">{errors.api}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Setting"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
