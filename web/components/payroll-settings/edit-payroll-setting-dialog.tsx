"use client";

import type React from "react";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import type { PayrollSetting } from "./payroll-settings-list";
import { api } from "@/lib/api";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

interface EditPayrollSettingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setting: PayrollSetting;
  onSubmit: () => void;
}

function toTimeInput(str: string) {
  // Converts 'hh:mm AM/PM' to 'HH:mm' for input type="time"
  if (!str) return "";
  const [time, modifier] = str.split(" ");
  let [hours, minutes] = time.split(":").map(Number);
  if (modifier === "PM" && hours < 12) hours += 12;
  if (modifier === "AM" && hours === 12) hours = 0;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
}

function toAmPm(str: string) {
  // Converts 'HH:mm' to 'hh:mm AM/PM'
  if (!str) return "";
  let [hours, minutes] = str.split(":").map(Number);
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12 || 12;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")} ${ampm}`;
}

export function EditPayrollSettingDialog({
  open,
  onOpenChange,
  setting,
  onSubmit,
}: EditPayrollSettingDialogProps) {
  const [loading, setLoading] = useState(false);

  const getInitialFormData = () => ({
    id: setting.id,
    name: setting.name,
    startTime: toTimeInput(setting.startTime),
    endTime: toTimeInput(setting.endTime),
    breakTime: setting.breakTime,
    rate: setting.rate,
    createdAt: setting.createdAt,
    is_base_price: setting.is_base_price || false,
  });

  const initialErrors = {
    name: "",
    startTime: "",
    endTime: "",
    breakTime: "",
    rate: "",
    api: "",
  };
  const [formData, setFormData] = useState(getInitialFormData);
  const [errors, setErrors] = useState(initialErrors);

  useEffect(() => {
    if (open) {
      // Reset form data when dialog opens with fresh setting data
      setFormData(getInitialFormData());
      setErrors(initialErrors);
      setLoading(false);
    } else {
      // Reset form when dialog is closed
      setErrors(initialErrors);
      setLoading(false);
    }
  }, [setting, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    if (name === "rate" || name === "breakTime") {
      const numValue = Number.parseFloat(value);
      setFormData((prev) => ({
        ...prev,
        [name]: isNaN(numValue) ? 0 : numValue,
      }));
    } else if (type === "checkbox") {
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
    setErrors((prev) => ({ ...prev, [name]: "", api: "" }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      startTime: "",
      endTime: "",
      breakTime: "",
      rate: "",
      api: "",
    };
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
      isValid = false;
    }
    if (!formData.startTime) {
      newErrors.startTime = "Start time is required";
      isValid = false;
    }
    if (!formData.endTime) {
      newErrors.endTime = "End time is required";
      isValid = false;
    }
    if (formData.rate <= 0) {
      newErrors.rate = "Rate must be greater than 0";
      isValid = false;
    }
    if (formData.breakTime < 0) {
      newErrors.breakTime = "Break time cannot be negative";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    try {
      const updated = await api.timeSlots.update(Number(formData.id), {
        name: formData.name,
        start_time: toAmPm(formData.startTime),
        end_time: toAmPm(formData.endTime),
        break_time: formData.breakTime,
        rate: formData.rate,
        is_base_price: formData.is_base_price,
      });

      onOpenChange(false);
      onSubmit();
    } catch (err: any) {
      toast.error(err.message || "Failed to update time slot");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Payroll Setting</DialogTitle>
          <DialogDescription>
            Update the time-based payroll rate setting.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Setting Name</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Morning Shift"
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-startTime">Start Time</Label>
                <Input
                  id="edit-startTime"
                  name="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={handleChange}
                />
                {errors.startTime && (
                  <p className="text-sm text-red-500">{errors.startTime}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-endTime">End Time</Label>
                <Input
                  id="edit-endTime"
                  name="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={handleChange}
                />
                {errors.endTime && (
                  <p className="text-sm text-red-500">{errors.endTime}</p>
                )}
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-breakTime">Break Time (minutes)</Label>
              <Input
                id="edit-breakTime"
                name="breakTime"
                type="number"
                min="0"
                step="5"
                value={formData.breakTime}
                onChange={handleChange}
              />
              {errors.breakTime && (
                <p className="text-sm text-red-500">{errors.breakTime}</p>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-rate">Rate (SEK)</Label>
                <Input
                  id="edit-rate"
                  name="rate"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.rate}
                  onChange={handleChange}
                />
                {errors.rate && (
                  <p className="text-sm text-red-500">{errors.rate}</p>
                )}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-is_base_price">Base Price Setting</Label>
                <div className="flex items-center space-x-2 pt-2">
                  <Checkbox
                    id="edit-is_base_price"
                    name="is_base_price"
                    checked={formData.is_base_price}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({
                        ...prev,
                        is_base_price: checked === true,
                      }))
                    }
                  />
                  <Label
                    htmlFor="edit-is_base_price"
                    className="text-sm font-normal"
                  >
                    Mark as base price
                  </Label>
                </div>
              </div>
            </div>
            {errors.api && <p className="text-sm text-red-500">{errors.api}</p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
