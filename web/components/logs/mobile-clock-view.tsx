"use client"

import { useState } from "react"
import { format } from "date-fns"
import { Clock, ClockIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface MobileClockViewProps {
  employees: { id: string; name: string }[]
  onClockIn: (employeeId: string, notes?: string) => void
  onClockOut: (employeeId: string, notes?: string) => void
  isActive: (employeeId: string) => boolean
}

export function MobileClockView({ employees, onClockIn, onClockOut, isActive }: MobileClockViewProps) {
  const [selectedEmployee, setSelectedEmployee] = useState<string>("")
  const [notes, setNotes] = useState<string>("")
  const [currentTime, setCurrentTime] = useState<string>(format(new Date(), "h:mm:ss a"))

  // Update the clock every second
  useState(() => {
    const timer = setInterval(() => {
      setCurrentTime(format(new Date(), "h:mm:ss a"))
    }, 1000)

    return () => clearInterval(timer)
  })

  const handleClockIn = () => {
    if (selectedEmployee) {
      onClockIn(selectedEmployee, notes.trim() || undefined)
      setNotes("")
    }
  }

  const handleClockOut = () => {
    if (selectedEmployee) {
      onClockOut(selectedEmployee, notes.trim() || undefined)
      setNotes("")
    }
  }

  const isEmployeeActive = selectedEmployee ? isActive(selectedEmployee) : false

  return (
    <div className="flex flex-col items-center max-w-md mx-auto">
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-center">Employee Time Clock</CardTitle>
          <CardDescription className="text-center">Clock in and out for your shifts</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Digital Clock */}
          <div className="text-center">
            <div className="text-4xl font-mono font-bold bg-muted rounded-lg py-4 px-6 inline-block">{currentTime}</div>
            <div className="text-sm text-muted-foreground mt-2">{format(new Date(), "EEEE, MMMM d, yyyy")}</div>
          </div>

          {/* Employee Selection */}
          <div className="space-y-2">
            <Label htmlFor="employee">Select Employee</Label>
            <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
              <SelectTrigger id="employee">
                <SelectValue placeholder="Select your name" />
              </SelectTrigger>
              <SelectContent>
                {employees.map((employee) => (
                  <SelectItem key={employee.id} value={employee.id}>
                    {employee.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about your shift"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>

          {/* Status */}
          {selectedEmployee && (
            <div className="text-center py-2">
              <div className="text-sm font-medium">Current Status:</div>
              <div className="flex items-center justify-center gap-2 mt-1">
                <div className={`h-3 w-3 rounded-full ${isEmployeeActive ? "bg-green-500" : "bg-gray-300"}`} />
                <span>{isEmployeeActive ? "Clocked In" : "Clocked Out"}</span>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center gap-4">
          <Button
            variant="default"
            size="lg"
            className="w-32"
            onClick={handleClockIn}
            disabled={!selectedEmployee || isEmployeeActive}
          >
            <Clock className="mr-2 h-4 w-4" />
            Clock In
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="w-32"
            onClick={handleClockOut}
            disabled={!selectedEmployee || !isEmployeeActive}
          >
            <ClockIcon className="mr-2 h-4 w-4" />
            Clock Out
          </Button>
        </CardFooter>
      </Card>

      {/* Mobile Instructions */}
      <div className="mt-8 text-sm text-muted-foreground">
        <h3 className="font-medium mb-2">Instructions:</h3>
        <ol className="list-decimal pl-5 space-y-1">
          <li>Select your name from the dropdown</li>
          <li>Click "Clock In" when you start your shift</li>
          <li>Add notes about your tasks if needed</li>
          <li>Click "Clock Out" when you finish your shift</li>
          <li>All time logs require manager approval</li>
        </ol>
      </div>
    </div>
  )
}
