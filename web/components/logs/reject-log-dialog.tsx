"use client";

import { useState } from "react";
import { format, parseISO } from "date-fns";
import { X, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

import type { LogEntry } from "./logs-list";
import { formatUTCCustom } from "./view-log-dialog";

interface RejectLogDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  log: LogEntry;
  employeeName: string;
  onConfirm: (logId: string, notes: string) => void;
  isSubmitting?: boolean;
}

export function RejectLogDialog({
  open,
  onOpenChange,
  log,
  employeeName,
  onConfirm,
  isSubmitting = false,
}: RejectLogDialogProps) {
  const [notes, setNotes] = useState("");
  const [error, setError] = useState("");

  const handleConfirm = () => {
    if (!notes.trim()) {
      setError("Please provide a reason for rejection");
      return;
    }
    onConfirm(log.id, notes);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Reject Time Log</DialogTitle>
          <DialogDescription>
            You are about to reject {employeeName}'s time log
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Employee:</span>
            <span>{employeeName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock In:</span>
            <span>{formatUTCCustom(log?.clock_in_at)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock Out:</span>
            <span>
              {log?.clock_out_at ? formatUTCCustom(log.clock_out_at) : "-"}
            </span>
          </div>
          <Separator />
          <div className="space-y-2">
            <label htmlFor="notes" className="text-sm font-medium">
              Reason for Rejection (Required):
            </label>
            <Textarea
              id="notes"
              placeholder="Explain why this time log is being rejected..."
              value={notes}
              onChange={(e) => {
                setNotes(e.target.value);
                if (e.target.value.trim()) {
                  setError("");
                }
              }}
              rows={3}
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        </div>
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            variant="destructive"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Rejecting...
              </>
            ) : (
              <>
                <X className="mr-2 h-4 w-4" />
                Reject Log
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
