"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Pencil } from "lucide-react";
import { api } from "@/lib/api";
import type { LogEntry } from "./logs-list";
import { errorParser } from "@/lib/error-parser";

interface EditLogTimesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  log: LogEntry | null;
  onSubmit: () => void;
}

export function EditLogTimesDialog({
  open,
  onOpenChange,
  log,
  onSubmit,
}: EditLogTimesDialogProps) {
  const [formData, setFormData] = useState({
    clock_in_at: "",
    clock_out_at: "",
    break_start: "",
    break_end: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // Helper to convert UTC ISO string to datetime-local format (without timezone conversion)
  function toDateTimeLocalUTC(val?: string | null) {
    if (!val) return "";
    const d = new Date(val);
    if (isNaN(d.getTime())) return "";

    // Use UTC methods to avoid timezone conversion
    const pad = (n: number) => n.toString().padStart(2, "0");
    return (
      d.getUTCFullYear() +
      "-" +
      pad(d.getUTCMonth() + 1) +
      "-" +
      pad(d.getUTCDate()) +
      "T" +
      pad(d.getUTCHours()) +
      ":" +
      pad(d.getUTCMinutes())
    );
  }

  useEffect(() => {
    if (open && log) {
      setFormData({
        clock_in_at: toDateTimeLocalUTC(log.clock_in_at),
        clock_out_at: toDateTimeLocalUTC(log.clock_out_at),
        break_start: toDateTimeLocalUTC(log.break_start),
        break_end: toDateTimeLocalUTC(log.break_end),
      });
      setError("");
    }
  }, [open, log]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setError("");
  };

  // Helper to convert datetime-local to "YYYY-MM-DD hh:mm AM/PM" format in UTC
  function toAmPmFormatUTC(val: string) {
    if (!val) return null;

    // Parse the datetime-local value as UTC
    const d = new Date(val + ":00.000Z"); // Add seconds and Z to make it explicit UTC
    if (isNaN(d.getTime())) return null;

    let hours = d.getUTCHours();
    const minutes = d.getUTCMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    hours = hours % 12;
    if (hours === 0) hours = 12;

    const pad = (n: number) => n.toString().padStart(2, "0");
    return (
      d.getUTCFullYear() +
      "-" +
      pad(d.getUTCMonth() + 1) +
      "-" +
      pad(d.getUTCDate()) +
      " " +
      pad(hours) +
      ":" +
      pad(minutes) +
      " " +
      ampm
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!log) return;
    setLoading(true);
    setError("");
    try {
      await api.scheduleLogs.editTime({
        id: Number(log.id),
        clock_in_at: formData.clock_in_at
          ? toAmPmFormatUTC(formData.clock_in_at)
          : null,
        clock_out_at: formData.clock_out_at
          ? toAmPmFormatUTC(formData.clock_out_at)
          : null,
        break_start: formData.break_start
          ? toAmPmFormatUTC(formData.break_start)
          : null,
        break_end: formData.break_end
          ? toAmPmFormatUTC(formData.break_end)
          : null,
      });
      onSubmit();
      onOpenChange(false);
    } catch (err: any) {
      const error = errorParser(err);
      setError(error || "Failed to update times");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Log Times</DialogTitle>
          <DialogDescription>
            Update the clock in/out and break times for this log.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-clock_in_at">Clock In </Label>
              <Input
                id="edit-clock_in_at"
                name="clock_in_at"
                type="datetime-local"
                value={formData.clock_in_at}
                onChange={handleChange}
                placeholder="Clock In"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-break_start">Break Start</Label>
              <Input
                id="edit-break_start"
                name="break_start"
                type="datetime-local"
                value={formData.break_start}
                onChange={handleChange}
                placeholder="Break Start"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-break_end">Break End</Label>
              <Input
                id="edit-break_end"
                name="break_end"
                type="datetime-local"
                value={formData.break_end}
                onChange={handleChange}
                placeholder="Break End"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-clock_out_at">Clock Out</Label>
              <Input
                id="edit-clock_out_at"
                name="clock_out_at"
                type="datetime-local"
                value={formData.clock_out_at}
                onChange={handleChange}
                placeholder="Clock Out"
              />
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
