"use client";

import { format, parseISO } from "date-fns";
import { Check, X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import type { LogEntry } from "./logs-list";
import { formatDuration } from "./logs-table";

interface ViewLogDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  log: LogEntry;
  employeeName: string;
  onApprove: () => void;
  onReject: () => void;
}

export const formatUTCCustom = (dateString: any) => {
  const date = new Date(dateString);
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const month = months[date.getUTCMonth()];
  const day = date.getUTCDate();
  const year = date.getUTCFullYear();
  let hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  if (hours === 0) hours = 12;

  const minutesStr = minutes.toString().padStart(2, "0");

  return `${month} ${day}, ${year} ${hours}:${minutesStr} ${ampm}`;
};

export function ViewLogDialog({
  open,
  onOpenChange,
  log,
  employeeName,
  onApprove,
  onReject,
}: ViewLogDialogProps) {
  // Format duration in hours

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "started":
        return <Badge className="bg-blue-50 text-blue-600">Started</Badge>;
      case "pending":
        return <Badge className="bg-amber-50 text-amber-600">Pending</Badge>;
      case "approved":
        return <Badge className="bg-green-50 text-green-600">Approved</Badge>;
      case "rejected":
        return <Badge className="bg-red-50 text-red-600">Rejected</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-xl max-h-[85vh] flex flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle>Time Log Details</DialogTitle>
          <DialogDescription>
            View details for {employeeName}'s time log
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-2 flex-1 overflow-y-auto pr-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Status:</span>
            <span>{getStatusBadge(log.status)}</span>
          </div>
          <Separator />
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Employee:</span>
            <span>{employeeName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock In:</span>
            <span>{formatUTCCustom(log?.clock_in_at)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock Out:</span>
            <span>
              {log?.clock_out_at ? formatUTCCustom(log.clock_out_at) : "-"}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Break Start:</span>
            <span>
              {log?.break_start ? formatUTCCustom(log?.break_start) : "-"}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Break End:</span>
            <span>{log.break_end ? formatUTCCustom(log?.break_end) : "-"}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Duration:</span>
            <span>{formatDuration(log?.duration)}</span>
          </div>
          <Separator />

          {/* Regular Pay Table */}
          <h5 className="text-sm font-semibold mb-2">Regular Pay</h5>
          <div className="rounded-md border overflow-hidden mb-4">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-background sticky top-0 z-10">
                  <TableRow>
                    <TableHead className="whitespace-nowrap">Name</TableHead>
                    <TableHead className="whitespace-nowrap">Hour</TableHead>
                    <TableHead className="whitespace-nowrap text-right">
                      Rate
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.isArray(log.work_pay_split) &&
                  log.work_pay_split.length > 0 ? (
                    log.work_pay_split.map((item, idx) => (
                      <TableRow key={idx}>
                        <TableCell className="whitespace-nowrap">
                          {item.shift?.name || "-"}
                        </TableCell>
                        <TableCell>{item?.hours_worked}</TableCell>
                        <TableCell className="text-right">
                          SEK {item.amount?.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={3}
                        className="text-center py-4 text-muted-foreground"
                      >
                        No regular pay data available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Overtime Pay Table */}
          <h5 className="text-sm font-semibold mb-2">Overtime Pay</h5>
          <div className="rounded-md border overflow-hidden mb-4">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-background sticky top-0 z-10">
                  <TableRow>
                    <TableHead className="whitespace-nowrap">Name</TableHead>
                    <TableHead className="whitespace-nowrap">Hour</TableHead>
                    <TableHead className="whitespace-nowrap text-right">
                      Rate
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.isArray(log.overtime_pay_split) &&
                  log.overtime_pay_split.length > 0 ? (
                    log.overtime_pay_split.map((item, idx) => (
                      <TableRow key={idx}>
                        <TableCell className="whitespace-nowrap">
                          {item.shift?.name || "-"}
                        </TableCell>
                        <TableCell>{item.hours_worked}</TableCell>
                        <TableCell className="text-right">
                          SEK {item.amount?.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={3}
                        className="text-center py-4 text-muted-foreground"
                      >
                        No overtime pay data available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Extra Pay */}
          {log.extra_pay !== null && log.extra_pay !== undefined && (
            <div className="flex justify-between items-center mt-2">
              <span className="text-base font-semibold">Extra Pay:</span>
              <span className="text-base font-bold">
                SEK {parseFloat(log.extra_pay || "0").toFixed(2)}
              </span>
            </div>
          )}

          {/* Total Pay */}
          <div className="flex justify-between items-center mt-2">
            <span className="text-base font-semibold">Total Pay:</span>
            <span className="text-base font-bold">
              SEK {parseFloat(log.total_pay || "0").toFixed(2)}
            </span>
          </div>

          <Separator />
          {log.note && (
            <div className="space-y-2">
              <span className="text-sm font-medium">Employee Notes:</span>
              <p className="text-sm bg-muted p-2 rounded">{log.note}</p>
            </div>
          )}
          {log.admin_note && (
            <div className="space-y-2">
              <span className="text-sm font-medium">Admin Notes:</span>
              <p className="text-sm bg-muted p-2 rounded">{log.admin_note}</p>
            </div>
          )}
        </div>
        <DialogFooter className="flex sm:justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
          {log.status === "pending" && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="border-green-500 text-green-600"
                onClick={onApprove}
              >
                <Check className="mr-2 h-4 w-4" />
                Approve
              </Button>
              <Button
                variant="outline"
                className="border-red-500 text-red-600"
                onClick={onReject}
              >
                <X className="mr-2 h-4 w-4" />
                Reject
              </Button>
            </div>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
