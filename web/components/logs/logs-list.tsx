"use client";

import { format, parseISO, subDays } from "date-fns";
import { Check, Clock, Filter, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { TopBar } from "@/components/top-bar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { api } from "@/lib/api";

import { DatePickerWithRange } from "../ui/date-range-picker";
import { ApproveLogDialog } from "./approve-log-dialog";
import { LogsTable } from "./logs-table";
import { MobileClockView } from "./mobile-clock-view";
import { RejectLogDialog } from "./reject-log-dialog";
import { ViewLogDialog } from "./view-log-dialog";

// Log entry type updated to match API response
export type LogEntry = {
  id: string;
  employee_id: string;
  clock_in_at: string;
  clock_out_at?: string;
  duration?: number;
  status: "pending" | "approved" | "rejected" | "started";
  note?: string;
  admin_note?: string;
  created_at: string;
  total_pay: string;
  overtime_pay: string;
  extra_pay?: string;
  work_pay_split: any;
  overtime_pay_split: any;
  break_start: string;
  break_end: string;
  work_pay: string;
  time_sheet_updated: boolean;
  employee?: {
    id: string;
    name: string;
    phone: string;
  };
};

// Employee type
type Employee = {
  id: string;
  name: string;
  phone: string;
  company_id: number;
  created_at: string;
  email: string;
  company: {
    id: number;
    name: string;
  };
};

// Add new type for Creator
type Creator = {
  id: string;
  email: string;
  raw_user_meta_data: { display_name: string } | null;
};

import { EditLogTimesDialog } from "./edit-log-times-dialog";

export function LogsList() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [creators, setCreators] = useState<Creator[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterEmployee, setFilterEmployee] = useState<string>("all");
  const [filterCreator, setFilterCreator] = useState<string>("all");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: subDays(new Date(), 7),
    to: new Date(),
  });
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalData, setTotalData] = useState(0);
  const [statsData, setStatsData] = useState<{
    counts: {
      pending: number;
      approved: number;
      rejected: number;
      completed: number;
    };
    total_duration: number;
  } | null>(null);

  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const [activeTab, setActiveTab] = useState<string>("admin");

  // Edit log times dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editLog, setEditLog] = useState<LogEntry | null>(null);

  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);

  const handleEditLog = (log: LogEntry) => {
    setEditLog(log);
    setIsEditDialogOpen(true);
  };

  // Fetch employees
  useEffect(() => {
    api.employees
      .list()
      .then((response) => {
        setEmployees(response?.employees || []);
      })
      .catch((error) => {
        console.error("Failed to fetch employees:", error);
      });
  }, []);

  // Fetch creators
  useEffect(() => {
    api.creators
      .list()
      .then((response) => {
        setCreators(
          (response?.users || []).map((user: any) => ({
            id: user.id,
            email: user.email,
            raw_user_meta_data: user.raw_user_meta_data || {
              display_name: null,
            },
          }))
        );
      })
      .catch((error) => {
        console.error("Failed to fetch creators:", error);
      });
  }, []);

  useEffect(() => {
    fetchLogs();
    fetchStats();
  }, [filterStatus, filterEmployee, filterCreator, dateRange, page, limit]);

  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const params: any = {
        page,
        limit,
      };

      if (filterStatus !== "all") {
        params.statuses = filterStatus;
      }

      if (filterEmployee !== "all") {
        params.employee_id = filterEmployee;
      }

      if (filterCreator != "all") {
        params.created_by = filterCreator;
      }

      if (dateRange.from) {
        params.start_date = format(dateRange.from, "yyyy-MM-dd");
      }

      if (dateRange.to) {
        params.end_date = format(dateRange.to, "yyyy-MM-dd");
      }

      const response = await api.scheduleLogs.listAdmin(params);

      setLogs(response.data || []);
      setTotalPages(response.paginate?.total_pages || 1);
      setTotalData(response.paginate?.total_data || 0);
    } catch (error) {
      console.error("Failed to fetch logs:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const params: any = {};

      if (filterEmployee !== "all") {
        params.employee_id = filterEmployee;
      }

      if (filterCreator !== "all") {
        params.created_by = filterCreator;
      }

      if (dateRange.from) {
        params.start_date = format(dateRange.from, "yyyy-MM-dd");
      }

      if (dateRange.to) {
        params.end_date = format(dateRange.to, "yyyy-MM-dd");
      }

      const response = await api.scheduleLogs.getStats(params);
      setStatsData(response);
    } catch (error) {
      console.error("Failed to fetch stats:", error);
    }
  };

  // Get employee name by ID
  const getEmployeeName = (id: string) => {
    const employee = employees.find((emp) => emp.id === id);
    return employee ? employee.name : "Unknown Employee";
  };

  // Handle view log
  const handleViewLog = (log: LogEntry) => {
    setSelectedLog(log);
    setIsViewDialogOpen(true);
  };

  // Handle approve log
  const handleApproveLog = (log: LogEntry) => {
    setSelectedLog(log);
    setIsApproveDialogOpen(true);
  };

  // Handle reject log
  const handleRejectLog = (log: LogEntry) => {
    setSelectedLog(log);
    setIsRejectDialogOpen(true);
  };

  // Submit approval
  const submitApproval = async (logId: string, notes?: string) => {
    setIsApproving(true);
    try {
      await api.scheduleLogs.approve(logId, { admin_note: notes });
      fetchLogs(); // Refresh logs after approval
      setIsApproveDialogOpen(false);
    } catch (error) {
      console.error("Failed to approve log:", error);
    } finally {
      setIsApproving(false);
    }
  };

  // Submit rejection
  const submitRejection = async (logId: string, notes: string) => {
    setIsRejecting(true);
    try {
      await api.scheduleLogs.reject(logId, { admin_note: notes });
      fetchLogs(); // Refresh logs after rejection
      setIsRejectDialogOpen(false);
    } catch (error) {
      console.error("Failed to reject log:", error);
    } finally {
      setIsRejecting(false);
    }
  };

  // Handle pagination
  const handleNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  // Export logs as CSV
  const exportLogs = () => {
    // Create CSV content
    const headers = [
      "Employee",
      "Clock In",
      "Clock Out",
      "Hours",
      "Status",
      "Notes",
      "Admin Notes",
    ];
    const csvContent = [
      headers.join(","),
      ...logs.map((log) =>
        [
          log.employee?.name || getEmployeeName(log.employee_id),
          format(parseISO(log.clock_in_at), "yyyy-MM-dd HH:mm:ss"),
          log.clock_out_at
            ? format(parseISO(log.clock_out_at), "yyyy-MM-dd HH:mm:ss")
            : "",
          log.duration || "",
          log.status,
          log.note || "",
          log.admin_note || "",
        ].join(",")
      ),
    ].join("\n");

    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `time_logs_${format(new Date(), "yyyy-MM-dd")}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Check if employee has active log
  const isEmployeeActive = (employeeId: string) => {
    return logs.some(
      (log) => log.employee_id === employeeId && log.status === "started"
    );
  };

  // Handle clock in
  const handleClockIn = async (employeeId: string, notes?: string) => {
    try {
      // Implementation logic...
      toast.success("Successfully clocked in");
    } catch (error) {
      toast.error("Failed to clock in");
    }
  };

  // Handle clock out
  const handleClockOut = async (employeeId: string, notes?: string) => {
    try {
      // Implementation logic...
      toast.success("Successfully clocked out");
    } catch (error) {
      toast.error("Failed to clock out");
    }
  };

  return (
    <>
      <DashboardSidebar />
      <div className="flex-1 overflow-auto h-screen">
        <TopBar title="Time Logs" />
        <main className="p-6 pb-24">
          <Tabs
            defaultValue="admin"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <TabsList className="mb-6">
              <TabsTrigger value="admin">Admin View</TabsTrigger>
              <TabsTrigger value="employee">Employee Clock In/Out</TabsTrigger>
            </TabsList>

            <TabsContent value="admin" className="space-y-6">
              {/* Stats Cards */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Hours
                    </CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {((statsData?.total_duration ?? 0) / 60).toFixed(2)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      In selected period
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      Pending Logs
                    </CardTitle>
                    <Clock className="h-4 w-4 text-amber-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {statsData?.counts?.pending || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Awaiting approval
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      Approved Logs
                    </CardTitle>
                    <Check className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {statsData?.counts?.approved || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Verified time entries
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      Rejected Logs
                    </CardTitle>
                    <X className="h-4 w-4 text-red-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {statsData?.counts?.rejected || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Disputed time entries
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">
                      Completed Logs
                    </CardTitle>
                    <Check className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {statsData?.counts?.completed || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Finished time entries
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Filters */}
              {/* <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="relative w-full max-w-sm">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search employees..."
                    className="w-full pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  <Button variant="outline" size="sm" onClick={exportLogs}>
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </Button>
                </div>
              </div> */}

              <div className="flex flex-col sm:flex-row gap-4 items-start pt-4 flex-wrap">
                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="status-filter">Status</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger id="status-filter" className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="started">Started</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="employee-filter">Employee</Label>
                  <Select
                    value={filterEmployee}
                    onValueChange={setFilterEmployee}
                  >
                    <SelectTrigger id="employee-filter" className="w-[180px]">
                      <SelectValue placeholder="Filter by employee" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Employees</SelectItem>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="creator-filter">Assigned By</Label>
                  <Select
                    value={filterCreator}
                    onValueChange={setFilterCreator}
                  >
                    <SelectTrigger id="creator-filter" className="w-[180px]">
                      <SelectValue placeholder="Filter by creator" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Assignees</SelectItem>
                      {creators.map((creator) => (
                        <SelectItem key={creator.id} value={creator.id}>
                          {creator?.raw_user_meta_data?.display_name ||
                            creator.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label>Date Range</Label>
                  <DatePickerWithRange
                    dateRange={dateRange}
                    //@ts-ignore
                    onDateRangeChange={setDateRange}
                  />
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label>&nbsp;</Label>
                  <Button
                    variant="outline"
                    size="default"
                    onClick={() => {
                      setSearchQuery("");
                      setFilterStatus("all");
                      setFilterEmployee("all");
                      setFilterCreator("all");
                      setDateRange({
                        from: subDays(new Date(), 7),
                        to: new Date(),
                      });
                      setPage(1);
                    }}
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    Clear Filters
                  </Button>
                </div>
              </div>

              {/* Logs Table */}
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : (
                <>
                  <LogsTable
                    logs={logs}
                    employees={employees}
                    onView={handleViewLog}
                    onApprove={handleApproveLog}
                    onReject={handleRejectLog}
                    onEdit={handleEditLog}
                  />

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-muted-foreground">
                      Showing {logs.length} of {totalData} entries
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePrevPage}
                        disabled={page === 1}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleNextPage}
                        disabled={page === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="employee">
              <MobileClockView
                employees={employees}
                onClockIn={handleClockIn}
                onClockOut={handleClockOut}
                isActive={isEmployeeActive}
              />
            </TabsContent>
          </Tabs>

          {/* View Log Dialog */}
          {selectedLog && (
            <ViewLogDialog
              open={isViewDialogOpen}
              onOpenChange={setIsViewDialogOpen}
              log={selectedLog}
              employeeName={
                selectedLog.employee?.name ||
                getEmployeeName(selectedLog.employee_id)
              }
              onApprove={() => {
                setIsViewDialogOpen(false);
                setIsApproveDialogOpen(true);
              }}
              onReject={() => {
                setIsViewDialogOpen(false);
                setIsRejectDialogOpen(true);
              }}
            />
          )}

          {/* Approve Log Dialog */}
          {selectedLog && (
            <ApproveLogDialog
              open={isApproveDialogOpen}
              onOpenChange={setIsApproveDialogOpen}
              log={selectedLog}
              employeeName={
                selectedLog.employee?.name ||
                getEmployeeName(selectedLog.employee_id)
              }
              onConfirm={submitApproval}
              isSubmitting={isApproving}
            />
          )}

          {/* Reject Log Dialog */}
          {selectedLog && (
            <RejectLogDialog
              open={isRejectDialogOpen}
              onOpenChange={setIsRejectDialogOpen}
              log={selectedLog}
              employeeName={
                selectedLog.employee?.name ||
                getEmployeeName(selectedLog.employee_id)
              }
              onConfirm={submitRejection}
              isSubmitting={isRejecting}
            />
          )}
          {/* Edit Log Times Dialog */}
          {editLog && (
            <EditLogTimesDialog
              open={isEditDialogOpen}
              onOpenChange={setIsEditDialogOpen}
              log={editLog}
              onSubmit={() => {
                setIsEditDialogOpen(false);
                setEditLog(null);
                fetchLogs();
              }}
            />
          )}
        </main>
      </div>
    </>
  );
}
