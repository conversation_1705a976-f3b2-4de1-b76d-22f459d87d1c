"use client";

import { format, parseISO } from "date-fns";
import { <PERSON>, <PERSON>, X } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import type { LogEntry } from "./logs-list";

import { Pencil } from "lucide-react";
import { formatUTCCustom } from "./view-log-dialog";

interface LogsTableProps {
  logs: LogEntry[];
  employees: { id: string; name: string }[];
  onView: (log: LogEntry) => void;
  onApprove: (log: LogEntry) => void;
  onReject: (log: LogEntry) => void;
  onEdit: (log: LogEntry) => void;
}

// Format duration in hours
export const formatDuration = (duration?: number) => {
  if (!duration) return "-";
  // Convert minutes to hours with 2 decimal places
  const hours = Math.floor((duration / 60) * 100) / 100;
  return `${hours} hrs`;
};

export function LogsTable({
  logs,
  employees,
  onView,
  onApprove,
  onReject,
  onEdit,
}: LogsTableProps) {
  // Get employee name
  const getEmployeeName = (id: string) => {
    const employee = employees.find((emp) => emp.id === id);
    return employee ? employee.name : "Unknown Employee";
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "started":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-600">
            Started
          </Badge>
        );
      case "pending":
        return (
          <Badge variant="outline" className="bg-amber-50 text-amber-600">
            Pending
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-600">
            Approved
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-600">
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Employee</TableHead>
            <TableHead>Clock In</TableHead>
            <TableHead>Clock Out</TableHead>
            <TableHead>Duration</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Notes</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={7}
                className="text-center py-8 text-muted-foreground"
              >
                No logs found
              </TableCell>
            </TableRow>
          ) : (
            logs.map((log) => (
              <TableRow key={log.id}>
                <TableCell className="font-medium">
                  {log.employee?.name || getEmployeeName(log.employee_id)}
                </TableCell>
                <TableCell>{formatUTCCustom(log?.clock_in_at)}</TableCell>
                <TableCell>
                  {log?.clock_out_at ? formatUTCCustom(log.clock_out_at) : "-"}
                </TableCell>
                <TableCell>{formatDuration(log.duration)}</TableCell>
                <TableCell>{getStatusBadge(log.status)}</TableCell>
                <TableCell>
                  {log.note ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="text-xs text-muted-foreground underline">
                          View Note
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">{log.note}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onView(log)}
                    >
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View</span>
                    </Button>
                    {!log?.time_sheet_updated && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onEdit(log)}
                      >
                        <Pencil className="h-4 w-4" />
                        <span className="sr-only">Edit Times</span>
                      </Button>
                    )}
                    {log.status === "pending" && (
                      <>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-green-600"
                          onClick={() => onApprove(log)}
                        >
                          <Check className="h-4 w-4" />
                          <span className="sr-only">Approve</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-600"
                          onClick={() => onReject(log)}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Reject</span>
                        </Button>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
