"use client";

import { useState } from "react";
import { format, parseISO } from "date-fns";
import { Check, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

import type { LogEntry } from "./logs-list";
import { formatUTCCustom } from "./view-log-dialog";

interface ApproveLogDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  log: LogEntry;
  employeeName: string;
  onConfirm: (logId: string, notes?: string) => void;
  isSubmitting?: boolean;
}

export function ApproveLogDialog({
  open,
  onOpenChange,
  log,
  employeeName,
  onConfirm,
  isSubmitting,
}: ApproveLogDialogProps) {
  const [notes, setNotes] = useState("");

  const handleConfirm = () => {
    onConfirm(log.id, notes.trim() || undefined);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Approve Time Log</DialogTitle>
          <DialogDescription>
            You are about to approve {employeeName}'s time log
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Employee:</span>
            <span>{employeeName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock In:</span>
            <span>{formatUTCCustom(log?.clock_in_at)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Clock Out:</span>
            <span>
              {log?.clock_out_at ? formatUTCCustom(log.clock_out_at) : "-"}
            </span>
          </div>
          <Separator />
          <div className="space-y-2">
            <label htmlFor="notes" className="text-sm font-medium">
              Admin Notes (Optional):
            </label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this approval..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>
        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-green-600 hover:bg-green-700"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Approving...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Approve Log
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
