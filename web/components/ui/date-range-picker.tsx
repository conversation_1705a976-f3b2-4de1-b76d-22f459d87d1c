"use client";

import React, { useState } from "react";
import DatePicker from "react-datepicker";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import type { DateRange } from "react-day-picker";
import "react-datepicker/dist/react-datepicker.css";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

interface DatePickerWithRangeProps
  extends React.HTMLAttributes<HTMLDivElement> {
  dateRange: DateRange;
  displayClassName: string;
  onDateRangeChange: (range: DateRange) => void;
}

export function DatePickerWithRange({
  className,
  displayClassName,
  dateRange,
  onDateRangeChange,
}: DatePickerWithRangeProps) {
  const [startDate, setStartDate] = useState<Date | null>(
    dateRange?.from || null
  );
  const [endDate, setEndDate] = useState<Date | null>(dateRange?.to || null);

  const handleChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);

    onDateRangeChange({
      from: start || undefined,
      to: end || undefined,
    });
  };

  return (
    <div className={cn("relative w-full", className)}>
      <style jsx global>{`
        .react-datepicker-wrapper {
          width: 100%;
        }
      `}</style>
      <DatePicker
        selectsRange={true}
        startDate={startDate}
        endDate={endDate}
        onChange={handleChange}
        customInput={
          <Button
            variant="outline"
            type="button"
            className={cn(
              "w-full justify-start text-left font-normal",
              displayClassName
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {startDate ? (
              endDate ? (
                <>
                  {format(startDate, "LLL dd, y")} -{" "}
                  {format(endDate, "LLL dd, y")}
                </>
              ) : (
                format(startDate, "LLL dd, y")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        }
        monthsShown={2}
        calendarClassName="bg-background border rounded-md shadow-md p-3 w-[260px]"
        dayClassName={(date) =>
          cn(
            "rounded-md hover:bg-muted",
            date >= (startDate || new Date(0)) &&
              date <= (endDate || new Date()) &&
              "bg-primary text-primary-foreground"
          )
        }
      />
    </div>
  );
}
