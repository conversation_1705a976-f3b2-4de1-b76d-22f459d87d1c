"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check authentication status
    const checkAuth = () => {
      const auth = localStorage.getItem("isAuthenticated")

      if (auth !== "true") {
        // Redirect to login if not authenticated
        router.replace("/login")
      } else {
        setIsAuthenticated(true)
      }

      setIsLoading(false)
    }

    checkAuth()
  }, [router])

  // Show nothing while checking authentication
  if (isLoading) {
    return null
  }

  // Only render children if authenticated
  return isAuthenticated ? <>{children}</> : null
}
