"use client"

import { useState } from "react"
import { format, parseISO } from "date-fns"

import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

import type { TimesheetEntry } from "./timesheet-list"

interface ApproveTimesheetDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  timesheet: TimesheetEntry
  employeeName: string
  onConfirm: (timesheetId: string, notes?: string) => void
}

export function ApproveTimesheetDialog({
  open,
  onOpenChange,
  timesheet,
  employeeName,
  onConfirm,
}: ApproveTimesheetDialogProps) {
  const [notes, setNotes] = useState<string>("")

  const handleConfirm = () => {
    onConfirm(timesheet.id, notes.trim() || undefined)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[85vh] flex flex-col overflow-hidden">
        <DialogHeader className="sticky top-0 z-10 bg-background pb-4 border-b">
          <DialogTitle>Approve Timesheet</DialogTitle>
          <DialogDescription>
            You are approving the timesheet for {employeeName} for the period{" "}
            {format(parseISO(timesheet.periodStart), "MMM d")} to {format(parseISO(timesheet.periodEnd), "MMM d, yyyy")}
            .
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 overflow-y-auto flex-1 pr-2">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Regular Hours</div>
                <div className="text-lg font-semibold">{timesheet.regularHours.toFixed(1)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Overtime Hours</div>
                <div className="text-lg font-semibold">{timesheet.overtimeHours.toFixed(1)}</div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Regular Pay</div>
                <div className="text-lg font-semibold">${timesheet.regularPay.toFixed(2)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Overtime Pay</div>
                <div className="text-lg font-semibold">${timesheet.overtimePay.toFixed(2)}</div>
              </div>
            </div>

            <div className="mt-2 p-2 bg-muted rounded-md">
              <div className="text-sm font-medium text-muted-foreground">Total Pay</div>
              <div className="text-xl font-bold">${timesheet.totalPay.toFixed(2)}</div>
            </div>

            <div>
              <Label htmlFor="notes">Admin Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this approval"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="mt-2"
              />
            </div>
          </div>
        </div>
        <DialogFooter className="sticky bottom-0 z-10 pt-4 bg-background border-t mt-auto">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>Approve Timesheet</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
