"use client";

import {
  addMonths,
  endOfMonth,
  format,
  startOfMonth,
  startOfYear,
  subDays,
  subMonths,
} from "date-fns";
import { Filter, Download } from "lucide-react";
import { useEffect, useState } from "react";

// Import API client
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { api } from "@/lib/api";

import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { TopBar } from "@/components/top-bar";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";
import { PayTimesheetDialog } from "./pay-timesheet-dialog";
import { TimesheetTable } from "./timesheet-table";
import { ViewTimesheetDialog } from "./view-timesheet-dialog";
import { toast } from "sonner";

// Import the component

// Sample employee data
const employees = [
  {
    id: "1",
    name: "John Doe",
    department: "Engineering",
    position: "Software Developer",
    hourlyTarget: 40,
    employeeId: "EMP001",
  },
  {
    id: "2",
    name: "Jane Smith",
    department: "Marketing",
    position: "Marketing Specialist",
    hourlyTarget: 38,
    employeeId: "EMP002",
  },
  {
    id: "3",
    name: "Michael Johnson",
    department: "Engineering",
    position: "QA Engineer",
    hourlyTarget: 40,
    employeeId: "EMP003",
  },
  {
    id: "4",
    name: "Emily Williams",
    department: "HR",
    position: "HR Manager",
    hourlyTarget: 35,
    employeeId: "EMP004",
  },
  {
    id: "5",
    name: "Robert Brown",
    department: "Finance",
    position: "Accountant",
    hourlyTarget: 40,
    employeeId: "EMP005",
  },
];

// Sample payroll settings
const payrollSettings: PayrollSetting[] = [
  {
    id: "1",
    name: "Morning Shift",
    startTime: "06:00",
    endTime: "14:00",
    breakTime: 60,
    hourlyRate: 18,
    overtimeRate: 27,
    createdAt: "2025-04-15T10:30:00Z",
  },
  {
    id: "2",
    name: "Evening Shift",
    startTime: "14:00",
    endTime: "22:00",
    breakTime: 30,
    hourlyRate: 22,
    overtimeRate: 33,
    createdAt: "2025-04-15T11:45:00Z",
  },
  {
    id: "3",
    name: "Night Shift",
    startTime: "22:00",
    endTime: "06:00",
    breakTime: 45,
    hourlyRate: 25,
    overtimeRate: 37.5,
    createdAt: "2025-04-16T09:15:00Z",
  },
];

// Sample projects
const projects = [
  { id: "p1", name: "Website Redesign", client: "Acme Corp", code: "PRJ001" },
  {
    id: "p2",
    name: "Mobile App Development",
    client: "TechStart Inc",
    code: "PRJ002",
  },
  {
    id: "p3",
    name: "Database Migration",
    client: "DataFlow Systems",
    code: "PRJ003",
  },
  { id: "p4", name: "Internal Tools", client: "Internal", code: "PRJ004" },
];

// Sample payment methods
const paymentMethods = ["Direct Deposit", "Check", "Wire Transfer", "PayPal"];

// Department data
const departments = [
  { id: "eng", name: "Engineering" },
  { id: "mkt", name: "Marketing" },
  { id: "hr", name: "HR" },
  { id: "fin", name: "Finance" },
];

// Define date presets
const DATE_PRESETS = {
  currentMonth: {
    label: "Current Month",
    value: "current_month",
    range: () => ({
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date()),
    }),
  },
  previousMonth: {
    label: "Previous Month",
    value: "previous_month",
    range: () => ({
      from: startOfMonth(subMonths(new Date(), 1)),
      to: endOfMonth(subMonths(new Date(), 1)),
    }),
  },
  last3Months: {
    label: "Last 3 Months",
    value: "last_3_months",
    range: () => ({
      from: subMonths(new Date(), 3),
      to: new Date(),
    }),
  },
  last6Months: {
    label: "Last 6 Months",
    value: "last_6_months",
    range: () => ({
      from: subMonths(new Date(), 6),
      to: new Date(),
    }),
  },
  yearToDate: {
    label: "Year to Date",
    value: "year_to_date",
    range: () => ({
      from: startOfYear(new Date()),
      to: new Date(),
    }),
  },
};

// Update TimesheetEntry type to match API response
export type TimesheetEntry = {
  id: number;
  employee_id: string;
  company_id: number;
  period: string;
  total_duration_allotted: number;
  total_duration: number;
  total_duration_worked: number;
  total_duration_overtime: number;
  total_pay: string | number;
  total_pay_worked: string | number;
  total_pay_overtime: string | number;
  extra_pay_total?: string | number;
  final_pay?: string | number | null;
  deduction?: string | number | null;
  status: string;
  payment_type?: string | null;
  paid_date?: string | null;
  meta?: any | null;
  note?: string | null;
  tax_meta?: {
    percentage: number;
  } | null;
  created_at: string;
  employee?: {
    id: string;
    name: string;
    phone?: string;
  };
};

export function TimesheetList() {
  const [timesheets, setTimesheets] = useState<TimesheetEntry[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterEmployee, setFilterEmployee] = useState<string>("all");
  const [selectedMonth, setSelectedMonth] = useState<Date>(new Date());
  const [datePreset, setDatePreset] = useState<string>(
    DATE_PRESETS.currentMonth.value
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [employees, setEmployees] = useState<any[]>([]);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isPayDialogOpen, setIsPayDialogOpen] = useState(false);
  const [selectedTimesheet, setSelectedTimesheet] =
    useState<TimesheetEntry | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Add state for timesheet stats
  const [timesheetStats, setTimesheetStats] = useState({
    total_duration_worked: 0,
    total_duration_overtime: 0,
    total_pay_worked: 0,
    total_pay_overtime: 0,
    paid_count: 0,
    not_paid_count: 0,
  });

  // Pagination
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalData, setTotalData] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Date range
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });

  // Fetch employees
  useEffect(() => {
    api.employees
      .list()
      .then((response) => {
        setEmployees(response.employees || []);
      })
      .catch((error) => {
        console.error("Failed to fetch employees:", error);
      });
  }, []);

  // Fetch timesheets and stats when filters change
  useEffect(() => {
    fetchTimesheets();
    fetchTimesheetStats();
  }, [page, limit, filterStatus, filterEmployee, dateRange]);

  const fetchTimesheets = async () => {
    setIsLoading(true);
    try {
      const params: any = {
        page,
        limit,
      };

      // Add filters
      if (filterEmployee !== "all") {
        params.employee_id = filterEmployee;
      }

      if (filterStatus !== "all") {
        params.status = filterStatus;
      }

      // Add date range
      if (dateRange.from) {
        params.start_date = format(dateRange.from, "yyyy-MM-dd");
      }
      if (dateRange.to) {
        params.end_date = format(dateRange.to, "yyyy-MM-dd");
      }

      const response = await api.timeSheets.list(params);

      setTimesheets(response.data || []);
      setTotalData(response.paginate?.total_data || 0);
      setTotalPages(response.paginate?.total_pages || 0);
    } catch (error) {
      console.error("Failed to fetch timesheets:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch timesheet stats
  const fetchTimesheetStats = async () => {
    try {
      const params: any = {};

      // Add filters
      if (filterEmployee !== "all") {
        params.employee_id = filterEmployee;
      }

      if (filterStatus !== "all") {
        params.status = filterStatus;
      }

      // Add date range
      if (dateRange.from) {
        params.start_date = format(dateRange.from, "yyyy-MM-dd");
      }
      if (dateRange.to) {
        params.end_date = format(dateRange.to, "yyyy-MM-dd");
      }

      const response = await api.timeSheets.getStats(params);
      setTimesheetStats(response);
    } catch (error) {
      console.error("Failed to fetch timesheet stats:", error);
    }
  };

  // Handle view timesheet
  const handleViewTimesheet = async (timesheet: TimesheetEntry) => {
    try {
      // Fetch detailed timesheet data
      const detailedTimesheet = await api.timeSheets.view(timesheet.id);
      setSelectedTimesheet(detailedTimesheet);
      setIsViewDialogOpen(true);
    } catch (error) {
      console.error("Failed to fetch timesheet details:", error);
    }
  };

  // Handle pay timesheet
  const handlePayTimesheet = (timesheet: TimesheetEntry) => {
    setSelectedTimesheet(timesheet);
    setIsPayDialogOpen(true);
  };

  // Submit payment
  const submitPayment = async (timesheetId: string, notes?: string) => {
    try {
      // Close dialog and refresh data
      // setIsPayDialogOpen(false);
      fetchTimesheets();
      fetchTimesheetStats();
    } catch (error) {
      console.error("Failed to submit payment:", error);
    }
  };

  // Get employee name
  const getEmployeeName = (employeeId: string) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    return employee ? employee.name : "Unknown Employee";
  };

  // Get employee department
  const getEmployeeDepartment = (employeeId: string) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    return employee ? employee.department || "Unassigned" : "Unassigned";
  };

  // Convert minutes to hours
  const minutesToHours = (minutes: number) => {
    return minutes / 60;
  };

  // Calculate statistics from the API response
  const totalWorkingHours = minutesToHours(
    timesheetStats.total_duration_worked || 0
  ).toFixed(1);
  const totalOvertimeHours = minutesToHours(
    timesheetStats.total_duration_overtime || 0
  ).toFixed(1);
  const totalRegularPay = parseFloat(
    timesheetStats.total_pay_worked?.toString() || "0"
  ).toFixed(2);
  const totalOvertimePay = parseFloat(
    timesheetStats.total_pay_overtime?.toString() || "0"
  ).toFixed(2);
  const notPaidCount = timesheetStats.not_paid_count || 0;
  const paidCount = timesheetStats.paid_count || 0;

  // Filter timesheets based on search query
  const filteredTimesheets = timesheets.filter((timesheet) => {
    const employeeName =
      timesheet.employee?.name || getEmployeeName(timesheet.employee_id);
    return employeeName.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Handle pagination
  const handleNextPage = () => {
    if (page < totalPages) {
      setPage(page + 1);
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  // Handle month navigation
  const previousMonth = () => {
    setSelectedMonth((prevMonth) => {
      const newDate = subMonths(prevMonth, 1);
      return newDate;
    });
  };

  const nextMonth = () => {
    setSelectedMonth((prevMonth) => {
      const newDate = addMonths(prevMonth, 1);
      return newDate;
    });
  };

  // Handle calendar select
  const handleCalendarSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedMonth(date);
      setIsCalendarOpen(false);
    }
  };

  // Handle date preset change
  const handleDatePresetChange = (value: string) => {
    setDatePreset(value);

    if (value === "custom") {
      // Keep current date range
      return;
    }

    // Find the preset
    const preset = Object.values(DATE_PRESETS).find((p) => p.value === value);
    if (preset) {
      setDateRange(preset.range());
    }
  };

  // Handle export timesheets
  const handleExportTimesheets = async () => {
    try {
      const params: any = {};

      // Add filters (same as fetchTimesheets)
      if (filterEmployee !== "all") {
        params.employee_id = filterEmployee;
      }

      if (filterStatus !== "all") {
        params.status = filterStatus;
      }

      // Add date range
      if (dateRange.from) {
        params.start_date = format(dateRange.from, "yyyy-MM-dd");
      }
      if (dateRange.to) {
        params.end_date = format(dateRange.to, "yyyy-MM-dd");
      }

      // Call the export API
      const blob = await api.timeSheets.export(params);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `timesheets_${format(new Date(), "yyyy-MM-dd")}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Timesheet export downloaded successfully");
    } catch (error: any) {
      console.error("Failed to export timesheets:", error);
      toast.error(error.message || "Failed to export timesheets");
    }
  };

  return (
    <>
      <DashboardSidebar />
      <div className="flex-1 overflow-auto h-screen">
        <TopBar title="Timesheet Management" />
        <main className="p-6 pb-24">
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Working Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalWorkingHours}</div>
                  <p className="text-xs text-muted-foreground">
                    In selected period
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Overtime Hours
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalOvertimeHours}</div>
                  <p className="text-xs text-muted-foreground">
                    In selected period
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Not Paid
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{notPaidCount}</div>
                  <p className="text-xs text-muted-foreground">
                    Pending payment
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">Paid</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{paidCount}</div>
                  <p className="text-xs text-muted-foreground">
                    Completed payments
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Regular Pay
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    SEK {totalRegularPay}
                  </div>
                  {/* <p className="text-xs text-muted-foreground">
                    In selected period
                  </p> */}
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">
                    Overtime Pay
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    SEK {totalOvertimePay}
                  </div>
                  {/* <p className="text-xs text-muted-foreground">
                    In selected period
                  </p> */}
                </CardContent>
              </Card>
            </div>

            {/* Filters and Actions */}
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <div className="flex flex-col sm:flex-row gap-4">
                {/* <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      type="search"
                      placeholder="Search timesheets..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div> */}

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="status-filter">Status</Label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger id="status-filter" className="w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="not_paid">Not Paid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label htmlFor="employee-filter">Employee</Label>
                  <Select
                    value={filterEmployee}
                    onValueChange={setFilterEmployee}
                  >
                    <SelectTrigger id="employee-filter" className="w-[180px]">
                      <SelectValue placeholder="Filter by employee" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Employees</SelectItem>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label>Date Range</Label>
                  <DatePickerWithRange
                    dateRange={dateRange}
                    onDateRangeChange={setDateRange}
                  />
                </div>

                <div className="flex flex-col gap-2 w-full sm:w-auto">
                  <Label>&nbsp;</Label>
                  <Button
                    variant="outline"
                    size="default"
                    onClick={() => {
                      setSearchQuery("");
                      setFilterStatus("all");
                      setFilterEmployee("all");
                      setDateRange({
                        from: subDays(new Date(), 30),
                        to: new Date(),
                      });
                      setPage(1);
                    }}
                  >
                    <Filter className="mr-2 h-4 w-4" />
                    Clear Filters
                  </Button>
                </div>
              </div>
              <div className="flex flex-col gap-2 w-full sm:w-auto">
                <Label>&nbsp;</Label>
                <Button
                  variant="outline"
                  size="default"
                  onClick={handleExportTimesheets}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>

            {/* Timesheet Table */}
            <TimesheetTable
              timesheets={timesheets}
              employees={employees}
              onView={handleViewTimesheet}
              onPay={handlePayTimesheet}
              viewType="monthly"
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {timesheets.length} of {totalData} timesheets
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevPage}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <div className="text-sm">
                    Page {page} of {totalPages}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}

            {/* <div className="mt-8 mb-4">
              <h2 className="text-lg font-semibold mb-4">
                Quick Payroll Calculator
              </h2>
              <PayrollCalculator payrollSettings={payrollSettings} />
            </div> */}
          </div>

          {/* View Timesheet Dialog */}
          {selectedTimesheet && (
            <ViewTimesheetDialog
              open={isViewDialogOpen}
              onOpenChange={setIsViewDialogOpen}
              timesheet={selectedTimesheet}
              employeeName={
                selectedTimesheet.employee?.name ||
                getEmployeeName(selectedTimesheet.employee_id)
              }
              employeeDepartment={getEmployeeDepartment(
                selectedTimesheet.employee_id
              )}
              payrollSettings={payrollSettings}
              projects={projects}
            />
          )}

          {/* Pay Timesheet Dialog */}
          {selectedTimesheet && (
            <PayTimesheetDialog
              open={isPayDialogOpen}
              onOpenChange={setIsPayDialogOpen}
              timesheet={selectedTimesheet}
              onConfirm={submitPayment}
              employeeName={
                selectedTimesheet.employee?.name ||
                getEmployeeName(selectedTimesheet.employee_id)
              }
            />
          )}
        </main>
      </div>
    </>
  );
}
