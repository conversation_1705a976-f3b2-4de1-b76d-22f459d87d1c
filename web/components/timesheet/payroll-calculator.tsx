"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { PayrollSetting } from "../payroll-settings/payroll-settings-list"

interface PayrollCalculatorProps {
  payrollSettings: PayrollSetting[]
}

export function PayrollCalculator({ payrollSettings }: PayrollCalculatorProps) {
  const [regularHours, setRegularHours] = useState<number>(0)
  const [overtimeHours, setOvertimeHours] = useState<number>(0)
  const [selectedRateId, setSelectedRateId] = useState<string>(payrollSettings[0]?.id || "")
  const [result, setResult] = useState<{
    regularPay: number
    overtimePay: number
    totalPay: number
  } | null>(null)

  const calculatePay = () => {
    const selectedRate = payrollSettings.find((setting) => setting.id === selectedRateId)

    if (!selectedRate) return

    const regularPay = regularHours * selectedRate.hourlyRate
    const overtimePay = overtimeHours * selectedRate.overtimeRate
    const totalPay = regularPay + overtimePay

    setResult({
      regularPay,
      overtimePay,
      totalPay,
    })
  }

  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle>Payroll Calculator</CardTitle>
        <CardDescription>Calculate employee pay based on hours worked and rate settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="regularHours">Regular Hours</Label>
            <Input
              id="regularHours"
              type="number"
              min="0"
              step="0.5"
              value={regularHours}
              onChange={(e) => setRegularHours(Number.parseFloat(e.target.value) || 0)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="overtimeHours">Overtime Hours</Label>
            <Input
              id="overtimeHours"
              type="number"
              min="0"
              step="0.5"
              value={overtimeHours}
              onChange={(e) => setOvertimeHours(Number.parseFloat(e.target.value) || 0)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="payRate">Pay Rate</Label>
          <Select value={selectedRateId} onValueChange={setSelectedRateId}>
            <SelectTrigger id="payRate">
              <SelectValue placeholder="Select a pay rate" />
            </SelectTrigger>
            <SelectContent>
              {payrollSettings.map((setting) => (
                <SelectItem key={setting.id} value={setting.id}>
                  {setting.name} (${setting.hourlyRate.toFixed(2)}/hr, OT: ${setting.overtimeRate.toFixed(2)}/hr)
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {result && (
          <div className="mt-6 p-4 bg-muted rounded-md">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Regular Pay</div>
                <div className="text-lg font-semibold">${result.regularPay.toFixed(2)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Overtime Pay</div>
                <div className="text-lg font-semibold">${result.overtimePay.toFixed(2)}</div>
              </div>
            </div>
            <div className="mt-4">
              <div className="text-sm font-medium text-muted-foreground">Total Pay</div>
              <div className="text-xl font-bold">${result.totalPay.toFixed(2)}</div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={calculatePay} className="w-full">
          Calculate Pay
        </Button>
      </CardFooter>
    </Card>
  )
}
