"use client";

import { useState, useEffect } from "react";
import { format, parseISO } from "date-fns";
import { Loader2, CreditCard } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

import type { TimesheetEntry } from "./timesheet-list";
import { api } from "@/lib/api";
import { toast } from "sonner";
import { formatPeriod } from "./view-timesheet-dialog";

interface TaxRate {
  id: number;
  company_id: number;
  percentage: number;
  created_at: string;
}

interface TaxCalculationResult {
  message: string;
  deduction: string | number;
  final_pay: string | number;
  tax: {
    percentage: number;
  };
  time_sheet_id: number;
}

interface PayTimesheetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  timesheet: TimesheetEntry;
  employeeName: string;
  onConfirm: (timesheetId: string, notes?: string) => void;
}

export function PayTimesheetDialog({
  open,
  onOpenChange,
  timesheet,
  employeeName,
  onConfirm,
}: PayTimesheetDialogProps) {
  const [notes, setNotes] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("direct_deposit");
  const [selectedTaxRate, setSelectedTaxRate] = useState<string>("");
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingTaxRates, setIsLoadingTaxRates] = useState(false);
  const [calculationResult, setCalculationResult] =
    useState<TaxCalculationResult | null>(null);
  const [showResults, setShowResults] = useState(false);

  // Convert minutes to hours
  const minutesToHours = (minutes: number) => {
    return minutes / 60;
  };

  // Fetch tax rates when dialog opens
  useEffect(() => {
    if (open) {
      fetchTaxRates();
      setCalculationResult(null);
      setSelectedTaxRate("");
      setNotes("");
      setShowResults(false);
    }
  }, [open]);

  const fetchTaxRates = async () => {
    setIsLoadingTaxRates(true);
    try {
      const response = await api.taxRates.list();
      setTaxRates(response?.items);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch tax rates");
      console.error("Failed to fetch tax rates:", error);
    } finally {
      setIsLoadingTaxRates(false);
    }
  };

  const handleConfirm = async () => {
    if (!selectedTaxRate) {
      toast.error("Please select a tax rate");
      return;
    }

    setIsSubmitting(true);
    try {
      const selectedPercentage = parseFloat(selectedTaxRate);
      if (isNaN(selectedPercentage)) {
        throw new Error("Invalid tax rate selected");
      }

      // Record payment with tax calculation
      const result = await api.timeSheets.recordPayment(timesheet.id, {
        payment_type: paymentMethod,
        note: notes || "",
        percentage: selectedPercentage,
      });

      // If the API returns tax calculation results, show them
      if (
        result &&
        result.deduction !== undefined &&
        result.final_pay !== undefined
      ) {
        setCalculationResult({
          message:
            result.message || "Tax applied and payment recorded successfully",
          deduction: result.deduction,
          final_pay: result.final_pay,
          tax: {
            percentage: selectedPercentage,
          },
          time_sheet_id: timesheet.id,
        });
        setShowResults(true);
      }

      toast.success("Tax applied and payment recorded successfully");
      onConfirm(String(timesheet.id), notes);

      // Don't close dialog immediately, let user see the results
      if (
        !result ||
        result.deduction === undefined ||
        result.final_pay === undefined
      ) {
        onOpenChange(false);
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to process payment");
      console.error("Failed to process payment:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Process Payment</DialogTitle>
          <DialogDescription>
            Process payment for {employeeName}'s timesheet for the period{" "}
            {formatPeriod(timesheet.period)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Working Hours
              </div>
              <div className="text-xl font-bold">
                {minutesToHours(timesheet.total_duration_worked || 0).toFixed(
                  1
                )}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Overtime Hours
              </div>
              <div className="text-xl font-bold">
                {minutesToHours(timesheet.total_duration_overtime || 0).toFixed(
                  1
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Regular Pay
              </div>
              <div className="text-xl font-bold">
                SEK&nbsp;
                {typeof timesheet.total_pay_worked === "string"
                  ? parseFloat(timesheet.total_pay_worked || "0").toFixed(2)
                  : (timesheet.total_pay_worked || 0).toFixed(2)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Overtime Pay
              </div>
              <div className="text-xl font-bold">
                SEK&nbsp;
                {typeof timesheet.total_pay_overtime === "string"
                  ? parseFloat(timesheet.total_pay_overtime || "0").toFixed(2)
                  : (timesheet.total_pay_overtime || 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="text-sm font-medium text-muted-foreground">
              Total Pay
            </div>
            <div className="text-2xl font-bold">
              SEK&nbsp;
              {typeof timesheet.total_pay === "string"
                ? parseFloat(timesheet.total_pay || "0").toFixed(2)
                : (timesheet.total_pay || 0).toFixed(2)}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="payment-method">Payment Method</Label>
            <Select
              value={paymentMethod}
              onValueChange={setPaymentMethod}
              disabled={showResults}
            >
              <SelectTrigger id="payment-method">
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="direct_deposit">Direct Deposit</SelectItem>
                <SelectItem value="cheque">Check</SelectItem>
                <SelectItem value="wire_transfer">Wire Transfer</SelectItem>
                <SelectItem value="paypal">PayPal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tax-rate">Tax Rate</Label>
            {isLoadingTaxRates ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading tax rates...
              </div>
            ) : (
              <Select
                value={selectedTaxRate}
                onValueChange={setSelectedTaxRate}
                disabled={showResults}
              >
                <SelectTrigger id="tax-rate">
                  <SelectValue placeholder="Select tax rate" />
                </SelectTrigger>
                <SelectContent>
                  {taxRates.map((rate) => (
                    <SelectItem
                      key={rate.id}
                      value={rate.percentage.toString()}
                    >
                      {rate.percentage}%
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Payment Notes</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this payment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              disabled={showResults}
            />
          </div>

          {calculationResult && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-lg font-semibold text-green-800 mb-3">
                Tax Calculation Result
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm font-medium text-green-700">
                    Tax Deduction ({calculationResult.tax.percentage}%)
                  </div>
                  <div className="text-xl font-bold text-green-800">
                    SEK{" "}
                    {typeof calculationResult.deduction === "string"
                      ? parseFloat(calculationResult.deduction).toFixed(2)
                      : calculationResult.deduction.toFixed(2)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-green-700">
                    Final Pay
                  </div>
                  <div className="text-xl font-bold text-green-800">
                    SEK{" "}
                    {typeof calculationResult.final_pay === "string"
                      ? parseFloat(calculationResult.final_pay).toFixed(2)
                      : calculationResult.final_pay.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {showResults ? (
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          ) : (
            <Button
              onClick={handleConfirm}
              disabled={isSubmitting || !selectedTaxRate || isLoadingTaxRates}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Apply Tax and Record Payment
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
