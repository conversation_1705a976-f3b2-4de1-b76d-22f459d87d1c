"use client"

import { useState } from "react"
import { format, parseISO } from "date-fns"
import { DollarSign, FileText } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import type { TimesheetEntry } from "./timesheet-list"
import type { PayrollSetting } from "../payroll-settings/payroll-settings-list"

interface TimesheetPayDetailsProps {
  timesheet: TimesheetEntry
  employeeName: string
  payrollSettings: PayrollSetting[]
}

export function TimesheetPayDetails({ timesheet, employeeName, payrollSettings }: TimesheetPayDetailsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Find the payroll setting used for this timesheet
  const payrollSetting = timesheet.payrollSettingId
    ? payrollSettings.find((setting) => setting.id === timesheet.payrollSettingId)
    : payrollSettings[0]

  // If no specific payroll setting is found, show a default message
  const hourlyRate = payrollSetting?.hourlyRate || 0
  const overtimeRate = payrollSetting?.overtimeRate || 0
  const payPeriod = `${format(parseISO(timesheet.periodStart), "MMM d")} - ${format(parseISO(timesheet.periodEnd), "MMM d, yyyy")}`

  // Generate payment receipt
  const generateReceipt = () => {
    // In a real app, this would generate a PDF or printable receipt
    alert("Payment receipt would be generated here")
  }

  return (
    <>
      <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
        <DollarSign className="mr-2 h-4 w-4" />
        Pay Details
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[85vh] flex flex-col overflow-hidden">
          <DialogHeader className="sticky top-0 z-10 bg-background pb-4 border-b">
            <DialogTitle>Timesheet Pay Details</DialogTitle>
            <DialogDescription>
              Pay information for {employeeName} for the period {payPeriod}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 overflow-y-auto flex-1 pr-2">
            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Working Hours Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${timesheet.regularPay.toFixed(2)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Overtime Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${timesheet.overtimePay.toFixed(2)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Gross Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${timesheet.totalPay.toFixed(2)}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Net Pay</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">${timesheet.netPay.toFixed(2)}</div>
                </CardContent>
              </Card>
            </div>

            {/* Rate Details */}
            <div className="bg-muted p-4 rounded-md">
              <h3 className="font-semibold mb-2">Rate Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">Rate Type</div>
                  <div className="font-medium">{payrollSetting?.name || "Default Rate"}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Regular Hourly Rate</div>
                  <div className="font-medium">${hourlyRate.toFixed(2)}/hr</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Overtime Rate</div>
                  <div className="font-medium">${overtimeRate.toFixed(2)}/hr</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Break Time</div>
                  <div className="font-medium">{payrollSetting?.breakTime || 0} minutes</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Pay Period</div>
                  <div className="font-medium">{payPeriod}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Tax Rate</div>
                  <div className="font-medium">{(timesheet.taxRate * 100).toFixed(1)}%</div>
                </div>
              </div>
            </div>

            {/* Calculation Details */}
            <div className="overflow-hidden rounded-md border">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="bg-background sticky top-0 z-10">
                    <TableRow>
                      <TableHead className="whitespace-nowrap">Description</TableHead>
                      <TableHead className="whitespace-nowrap">Hours</TableHead>
                      <TableHead className="whitespace-nowrap">Rate</TableHead>
                      <TableHead className="whitespace-nowrap text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>Working Hours</TableCell>
                      <TableCell>{timesheet.regularHours.toFixed(1)}</TableCell>
                      <TableCell>${hourlyRate.toFixed(2)}/hr</TableCell>
                      <TableCell className="text-right">${timesheet.regularPay.toFixed(2)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>Overtime Hours</TableCell>
                      <TableCell>{timesheet.overtimeHours.toFixed(1)}</TableCell>
                      <TableCell>${overtimeRate.toFixed(2)}/hr</TableCell>
                      <TableCell className="text-right">${timesheet.overtimePay.toFixed(2)}</TableCell>
                    </TableRow>
                    <TableRow className="font-medium">
                      <TableCell colSpan={3}>Gross Pay</TableCell>
                      <TableCell className="text-right">${timesheet.totalPay.toFixed(2)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell colSpan={3}>Tax Deductions ({(timesheet.taxRate * 100).toFixed(1)}%)</TableCell>
                      <TableCell className="text-right text-red-500">-${timesheet.taxDeductions.toFixed(2)}</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell colSpan={3}>Benefits Deductions</TableCell>
                      <TableCell className="text-right text-amber-500">
                        -${timesheet.benefitsDeductions.toFixed(2)}
                      </TableCell>
                    </TableRow>
                    <TableRow className="font-medium">
                      <TableCell colSpan={3}>Net Pay</TableCell>
                      <TableCell className="text-right text-green-600">${timesheet.netPay.toFixed(2)}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>

            {/* Payment Information */}
            {timesheet.status === "paid" && (
              <div className="bg-green-50 p-4 rounded-md border border-green-200">
                <h3 className="font-semibold mb-2 text-green-700">Payment Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-green-700">Payment Method</div>
                    <div className="font-medium">{timesheet.paymentMethod}</div>
                  </div>
                  <div>
                    <div className="text-sm text-green-700">Transaction ID</div>
                    <div className="font-medium">{timesheet.transactionId}</div>
                  </div>
                  <div>
                    <div className="text-sm text-green-700">Payment Reference</div>
                    <div className="font-medium">{timesheet.paymentReference}</div>
                  </div>
                  <div>
                    <div className="text-sm text-green-700">Payment Date</div>
                    <div className="font-medium">
                      {timesheet.paidAt ? format(parseISO(timesheet.paidAt), "PPp") : "N/A"}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="sticky bottom-0 z-10 pt-4 bg-background border-t mt-auto">
            {timesheet.status === "paid" && (
              <Button variant="outline" className="mr-auto" onClick={generateReceipt}>
                <FileText className="mr-2 h-4 w-4" />
                Generate Receipt
              </Button>
            )}
            <Button onClick={() => setIsDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
