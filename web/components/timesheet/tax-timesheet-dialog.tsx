"use client";

import { useState, useEffect } from "react";
import { Loader2, Receipt } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import type { TimesheetEntry } from "./timesheet-list";
import { api } from "@/lib/api";
import { toast } from "sonner";

interface TaxTimesheetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  timesheet: TimesheetEntry;
  employeeName: string;
  onConfirm: () => void;
}

interface TaxRate {
  id: number;
  company_id: number;
  percentage: number;
  created_at: string;
}

interface TaxCalculationResult {
  message: string;
  deduction: number;
  final_pay: number;
  tax: {
    percentage: number;
  };
  time_sheet_id: number;
}

export function TaxTimesheetDialog({
  open,
  onOpenChange,
  timesheet,
  employeeName,
  onConfirm,
}: TaxTimesheetDialogProps) {
  const [selectedTaxRate, setSelectedTaxRate] = useState<string>("");
  const [taxRates, setTaxRates] = useState<TaxRate[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingTaxRates, setIsLoadingTaxRates] = useState(false);
  const [calculationResult, setCalculationResult] =
    useState<TaxCalculationResult | null>(null);

  // Convert minutes to hours
  const minutesToHours = (minutes: number) => {
    return minutes / 60;
  };

  // Fetch tax rates when dialog opens
  useEffect(() => {
    if (open) {
      fetchTaxRates();
      setCalculationResult(null);
      setSelectedTaxRate("");
    }
  }, [open]);

  const fetchTaxRates = async () => {
    setIsLoadingTaxRates(true);
    try {
      const response = await api.taxRates.list();
      setTaxRates(response?.items);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch tax rates");
      console.error("Failed to fetch tax rates:", error);
    } finally {
      setIsLoadingTaxRates(false);
    }
  };

  const handleApplyTax = async () => {
    if (!selectedTaxRate) {
      toast.error("Please select a tax rate");
      return;
    }

    setIsSubmitting(true);
    try {
      const selectedPercentage = parseFloat(selectedTaxRate);
      if (isNaN(selectedPercentage)) {
        throw new Error("Invalid tax rate selected");
      }

      const result = await api.timeSheets.calculateTax(timesheet.id, {
        percentage: selectedPercentage,
      });

      setCalculationResult(result);
      onConfirm(); // Refresh the table data
      toast.success("Tax calculated and applied successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to calculate tax");
      console.error("Failed to calculate tax:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Calculate Tax</DialogTitle>
          <DialogDescription>
            Calculate and apply tax for {employeeName}'s timesheet for the
            period {timesheet.period}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Working Hours
              </div>
              <div className="text-xl font-bold">
                {minutesToHours(timesheet.total_duration_worked || 0).toFixed(
                  1
                )}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Overtime Hours
              </div>
              <div className="text-xl font-bold">
                {minutesToHours(timesheet.total_duration_overtime || 0).toFixed(
                  1
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Regular Pay
              </div>
              <div className="text-xl font-bold">
                SEK&nbsp;
                {typeof timesheet.total_pay_worked === "string"
                  ? parseFloat(timesheet.total_pay_worked || "0").toFixed(2)
                  : (timesheet.total_pay_worked || 0).toFixed(2)}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium text-muted-foreground">
                Overtime Pay
              </div>
              <div className="text-xl font-bold">
                SEK&nbsp;
                {typeof timesheet.total_pay_overtime === "string"
                  ? parseFloat(timesheet.total_pay_overtime || "0").toFixed(2)
                  : (timesheet.total_pay_overtime || 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="text-sm font-medium text-muted-foreground">
              Total Pay
            </div>
            <div className="text-2xl font-bold">
              SEK&nbsp;
              {typeof timesheet.total_pay === "string"
                ? parseFloat(timesheet.total_pay || "0").toFixed(2)
                : (timesheet.total_pay || 0).toFixed(2)}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tax-rate">Tax Rate</Label>
            {isLoadingTaxRates ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading tax rates...
              </div>
            ) : (
              <Select
                value={selectedTaxRate}
                onValueChange={setSelectedTaxRate}
              >
                <SelectTrigger id="tax-rate">
                  <SelectValue placeholder="Select tax rate" />
                </SelectTrigger>
                <SelectContent>
                  {taxRates.map((rate) => (
                    <SelectItem
                      key={rate.id}
                      value={rate.percentage.toString()}
                    >
                      {rate.percentage}%
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {calculationResult && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-lg font-semibold text-green-800 mb-3">
                Tax Calculation Result
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm font-medium text-green-700">
                    Tax Deduction ({calculationResult.tax.percentage}%)
                  </div>
                  <div className="text-xl font-bold text-green-800">
                    SEK {calculationResult.deduction.toFixed(2)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-green-700">
                    Final Pay
                  </div>
                  <div className="text-xl font-bold text-green-800">
                    SEK {calculationResult.final_pay.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleApplyTax}
            disabled={isSubmitting || !selectedTaxRate || isLoadingTaxRates}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Calculating...
              </>
            ) : (
              <>
                <Receipt className="mr-2 h-4 w-4" />
                Apply Tax
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
