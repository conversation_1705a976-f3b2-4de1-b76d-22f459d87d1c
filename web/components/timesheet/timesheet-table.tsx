"use client";

import { format, parseISO } from "date-fns";
import { Eye, FileText, CreditCard, Download } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import type { TimesheetEntry } from "./timesheet-list";

interface TimesheetTableProps {
  timesheets: TimesheetEntry[];
  employees: any[];
  onView: (timesheet: TimesheetEntry) => void;
  onPay: (timesheet: TimesheetEntry) => void;
  viewType: "monthly";
}

export function TimesheetTable({
  timesheets,
  employees,
  onView,
  onPay,
  viewType,
}: TimesheetTableProps) {
  // Get employee name by ID
  const getEmployeeName = (employeeId: string) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    return employee ? employee.name : "Unknown Employee";
  };

  // Convert minutes to hours
  const minutesToHours = (minutes: number) => {
    return minutes / 60;
  };

  // Format date range based on view type
  const formatDateRange = (timesheet: TimesheetEntry) => {
    try {
      const periodDate = parseISO(timesheet.period);
      return format(periodDate, "MMMM yyyy");
    } catch (error) {
      return timesheet.period || "Unknown Period";
    }
  };

  // Handle PDF download
  const handleDownloadPDF = (timesheet: TimesheetEntry) => {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    const pdfUrl = `${apiUrl}/time-sheets/${timesheet.id}/pdf`;
    window.open(pdfUrl, "_blank");
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Employee</TableHead>
            <TableHead>Period</TableHead>
            <TableHead>Total Allotted Hours</TableHead>
            <TableHead>Working Hours</TableHead>
            <TableHead>Overtime</TableHead>
            <TableHead>Total</TableHead>
            <TableHead>Total Pay</TableHead>
            <TableHead>Tax Deduction</TableHead>
            <TableHead>Final Pay</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right min-w-[120px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {timesheets.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={11}
                className="text-center py-8 text-muted-foreground"
              >
                No timesheets found
              </TableCell>
            </TableRow>
          ) : (
            timesheets.map((timesheet) => (
              <TableRow key={timesheet.id}>
                <TableCell>
                  <div className="">
                    {timesheet.employee?.name ||
                      getEmployeeName(timesheet.employee_id)}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDateRange(timesheet)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {minutesToHours(
                    timesheet.total_duration_allotted || 0
                  ).toFixed(1)}
                </TableCell>
                <TableCell>
                  {minutesToHours(timesheet.total_duration_worked || 0).toFixed(
                    1
                  )}
                </TableCell>
                <TableCell>
                  {minutesToHours(
                    timesheet.total_duration_overtime || 0
                  ).toFixed(1)}
                </TableCell>
                <TableCell className="">
                  {minutesToHours(timesheet.total_duration || 0).toFixed(1)}
                </TableCell>
                <TableCell className=" ">
                  SEK&nbsp;
                  {typeof timesheet.total_pay === "string"
                    ? parseFloat(timesheet.total_pay || "0").toFixed(2)
                    : (timesheet.total_pay || 0).toFixed(2)}
                </TableCell>
                <TableCell className=" ">
                  {timesheet.deduction ? (
                    <>
                      SEK&nbsp;
                      {typeof timesheet.deduction === "string"
                        ? parseFloat(timesheet.deduction || "0").toFixed(2)
                        : (timesheet.deduction || 0).toFixed(2)}
                    </>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell className=" ">
                  {timesheet.final_pay ? (
                    <>
                      SEK&nbsp;
                      {typeof timesheet.final_pay === "string"
                        ? parseFloat(timesheet.final_pay || "0").toFixed(2)
                        : (timesheet.final_pay || 0).toFixed(2)}
                    </>
                  ) : (
                    "-"
                  )}
                </TableCell>
                <TableCell>
                  {timesheet.status !== "paid" ? (
                    <Badge
                      variant="outline"
                      className="bg-amber-50 text-amber-700 border-amber-200"
                    >
                      Not Paid
                    </Badge>
                  ) : (
                    <Badge
                      variant="outline"
                      className="bg-green-50 text-green-700 border-green-200"
                    >
                      Paid
                    </Badge>
                  )}
                </TableCell>
                <TableCell className="text-right whitespace-nowrap">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onView(timesheet)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>View Details</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  {timesheet.status !== "paid" && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onPay(timesheet)}
                          >
                            <CreditCard className="h-4 w-4 text-green-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          Apply Tax and Record Payment
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  {timesheet.status === "paid" && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDownloadPDF(timesheet)}
                          >
                            <Download className="h-4 w-4 text-blue-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Download PDF</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
