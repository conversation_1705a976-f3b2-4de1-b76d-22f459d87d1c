"use client";

import { format, parseISO } from "date-fns";
import { Clock, CreditCard, DollarSign } from "lucide-react";
import { useEffect, useState } from "react";
import { api } from "@/lib/api";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

import type { TimesheetEntry } from "./timesheet-list";
// Import the TimesheetPayDetails component
import { TimesheetPayDetails } from "./timesheet-pay-details";
import type { PayrollSetting } from "../payroll-settings/payroll-settings-list";

// Update the interface to include payrollSettings and projects
interface ViewTimesheetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  timesheet: TimesheetEntry;
  employeeName: string;
  employeeDepartment: string;
  payrollSettings: PayrollSetting[];
  projects: { id: string; name: string; client: string; code: string }[];
}

// Format period to show month and year
export const formatPeriod = (periodString: string) => {
  try {
    const date = parseISO(periodString);
    return format(date, "MMMM yyyy");
  } catch (error) {
    return periodString;
  }
};

export function ViewTimesheetDialog({
  open,
  onOpenChange,
  timesheet,
  employeeName,
  employeeDepartment,
  payrollSettings,
  projects,
}: ViewTimesheetDialogProps) {
  // Find the payroll setting used for this timesheet
  const payrollSetting = payrollSettings[0];

  // Convert minutes to hours
  const minutesToHours = (minutes: number) => {
    return minutes / 60;
  };

  // Format date if available
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(parseISO(dateString), "PPp");
    } catch (error) {
      return dateString;
    }
  };

  // Add state for daily analytics
  const [dailyAnalytics, setDailyAnalytics] = useState<
    { date: string; day: string; total_duration: number }[]
  >([]);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);

  // Fetch daily analytics when timesheet changes
  useEffect(() => {
    if (open && timesheet?.id) {
      fetchDailyAnalytics(timesheet.id);
    }
  }, [open, timesheet?.id]);

  // Function to fetch daily analytics
  const fetchDailyAnalytics = async (timesheetId: number) => {
    setIsLoadingAnalytics(true);
    try {
      const analytics = await api.timeSheets.getDailyAnalytics(timesheetId);
      setDailyAnalytics(analytics);
    } catch (error) {
      console.error("Failed to fetch daily analytics:", error);
    } finally {
      setIsLoadingAnalytics(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[85vh] flex flex-col overflow-hidden">
        <DialogHeader className="sticky top-0 z-10 bg-background pb-4 border-b">
          <DialogTitle>Timesheet Details</DialogTitle>
          <DialogDescription>
            Viewing timesheet for {employeeName} for period{" "}
            {formatPeriod(timesheet.period)}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="summary" className="flex-1 overflow-hidden">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="hours">Hours</TabsTrigger>
          </TabsList>

          <div className="overflow-y-auto pr-2 flex-1">
            <TabsContent value="summary" className="space-y-6 mt-0">
              {/* Summary Section - Updated with API data */}
              <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Allotted Hours
                  </div>
                  <div className="text-2xl font-bold">
                    {minutesToHours(
                      timesheet.total_duration_allotted || 0
                    ).toFixed(1)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Working Hours
                  </div>
                  <div className="text-2xl font-bold">
                    {minutesToHours(
                      timesheet.total_duration_worked || 0
                    ).toFixed(1)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Overtime Hours
                  </div>
                  <div className="text-2xl font-bold">
                    {minutesToHours(
                      timesheet.total_duration_overtime || 0
                    ).toFixed(1)}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Total Hours
                  </div>
                  <div className="text-2xl font-bold">
                    {minutesToHours(timesheet.total_duration || 0).toFixed(1)}
                  </div>
                </div>
              </div>

              {/* Status and Payment */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Status
                  </div>
                  <div>
                    {timesheet.status === "paid" ? (
                      <Badge className="bg-green-50 text-green-700 border-green-200">
                        Paid
                      </Badge>
                    ) : (
                      <Badge className="bg-amber-50 text-amber-700 border-amber-200">
                        Not Paid
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Payment Date
                  </div>
                  <div>{formatDate(timesheet?.paid_date || undefined)}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium text-muted-foreground">
                    Payment Method
                  </div>
                  <div>{timesheet.payment_type || "N/A"}</div>
                </div>
              </div>

              {/* Pay Details */}
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Pay Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Regular Pay
                    </div>
                    <div className="text-xl font-bold">
                      SEK&nbsp;
                      {typeof timesheet.total_pay_worked === "string"
                        ? parseFloat(timesheet.total_pay_worked || "0").toFixed(
                            2
                          )
                        : (timesheet.total_pay_worked || 0).toFixed(2)}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Overtime Pay
                    </div>
                    <div className="text-xl font-bold">
                      SEK&nbsp;
                      {typeof timesheet.total_pay_overtime === "string"
                        ? parseFloat(
                            timesheet.total_pay_overtime || "0"
                          ).toFixed(2)
                        : (timesheet.total_pay_overtime || 0).toFixed(2)}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground">
                      Extra Pay Total
                    </div>
                    <div className="text-xl font-bold">
                      SEK&nbsp;
                      {typeof timesheet.extra_pay_total === "string"
                        ? parseFloat(timesheet?.extra_pay_total || "0").toFixed(
                            2
                          )
                        : (timesheet?.extra_pay_total || 0).toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* Tax Details - Show only if tax has been calculated */}
                {(timesheet.deduction || timesheet.final_pay) && (
                  <div className="grid pt-4 grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-muted-foreground">
                        Tax Deduction
                      </div>
                      <div className="text-xl font-bold text-red-600">
                        SEK&nbsp;
                        {timesheet.deduction
                          ? typeof timesheet.deduction === "string"
                            ? parseFloat(timesheet.deduction || "0").toFixed(2)
                            : (timesheet.deduction || 0).toFixed(2)
                          : "0.00"}
                        {timesheet.tax_meta?.percentage && (
                          <span className="text-sm text-muted-foreground ml-1">
                            ({timesheet.tax_meta.percentage}%)
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-muted-foreground">
                        Final Pay (After Tax)
                      </div>
                      <div className="text-xl font-bold text-green-600">
                        SEK&nbsp;
                        {timesheet.final_pay
                          ? typeof timesheet.final_pay === "string"
                            ? parseFloat(timesheet.final_pay || "0").toFixed(2)
                            : (timesheet.final_pay || 0).toFixed(2)
                          : "0.00"}
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium text-muted-foreground">
                        Gross Pay
                      </div>
                      <div className="text-xl font-bold">
                        SEK&nbsp;
                        {typeof timesheet.total_pay === "string"
                          ? parseFloat(timesheet.total_pay || "0").toFixed(2)
                          : (timesheet.total_pay || 0).toFixed(2)}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Notes */}
              {timesheet.note && (
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Notes</h3>
                  <div className="p-4 rounded-md bg-muted">
                    {timesheet.note}
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Keep the existing tabs as they are */}
            <TabsContent value="hours" className="space-y-6 mt-0">
              <div>
                <h3 className="text-lg font-semibold mb-2">Daily Hours</h3>
                <div className="rounded-md border overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader className="bg-background sticky top-0 z-10">
                        <TableRow>
                          <TableHead className="whitespace-nowrap">
                            Date
                          </TableHead>
                          <TableHead className="whitespace-nowrap">
                            Day
                          </TableHead>
                          <TableHead className="whitespace-nowrap">
                            Hours Worked
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoadingAnalytics ? (
                          <TableRow>
                            <TableCell colSpan={3} className="text-center py-4">
                              Loading daily analytics...
                            </TableCell>
                          </TableRow>
                        ) : dailyAnalytics.length === 0 ? (
                          <TableRow>
                            <TableCell
                              colSpan={3}
                              className="text-center py-4 text-muted-foreground"
                            >
                              No daily data available
                            </TableCell>
                          </TableRow>
                        ) : (
                          dailyAnalytics.map((day) => (
                            <TableRow key={day.date}>
                              <TableCell className="whitespace-nowrap">
                                {format(parseISO(day.date), "MMM d, yyyy")}
                              </TableCell>
                              <TableCell>{day.day}</TableCell>
                              <TableCell>
                                {minutesToHours(day.total_duration).toFixed(1)}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="sticky bottom-0 z-10 pt-4 bg-background border-t mt-auto gap-2 sm:gap-0">
          {/* <TimesheetPayDetails
            timesheet={timesheet}
            employeeName={employeeName}
            payrollSettings={payrollSettings}
          /> */}
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
