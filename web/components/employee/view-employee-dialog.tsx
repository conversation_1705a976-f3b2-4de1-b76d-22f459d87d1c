"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Employee } from "@/types/employee";

interface ViewEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee;
}

export function ViewEmployeeDialog({
  open,
  onO<PERSON><PERSON><PERSON><PERSON>,
  employee,
}: ViewEmployeeDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Employee Details</DialogTitle>
          <DialogDescription>
            View detailed information about this employee.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Name
              </h3>
              <p className="text-base">{employee.name}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Personal Number
              </h3>
              <p className="text-base">{employee.employee_number || "N/A"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Email
              </h3>
              <p className="text-base">{employee.email}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Phone Number
              </h3>
              <p className="text-base">{employee.phone}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Created
              </h3>
              <p className="text-base">
                {new Date(employee.created_at).toLocaleDateString()} at{" "}
                {new Date(employee.created_at).toLocaleTimeString()}
              </p>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
