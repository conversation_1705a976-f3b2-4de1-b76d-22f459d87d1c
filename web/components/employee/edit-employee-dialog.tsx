"use client";

import type React from "react";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import type { Employee } from "@/types/employee";
import { api } from "@/lib/api";
import { Loader2, Eye, EyeOff } from "lucide-react";

interface EditEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee;
  onSubmit: (employee: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    password?: string;
    employee_number?: string;
    has_driving_license?: boolean;
    id: number;
    company_id: number;
    created_at: string;
  }) => void;
}

export function EditEmployeeDialog({
  open,
  on<PERSON><PERSON><PERSON><PERSON><PERSON>,
  employee,
  onSubmit,
}: EditEmployeeDialogProps) {
  const [formData, setFormData] = useState(() => {
    return {
      id: employee.id,
      first_name: employee.first_name || "",
      last_name: employee.last_name || "",
      email: employee.email || "",
      phone: employee.phone || "",
      password: "", // Do not prepopulate password
      employee_number: employee.employee_number || "",
      has_driving_license: (employee as any).has_driving_license || false,
      created_at: employee.created_at,
    };
  });

  const [errors, setErrors] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    password: "",
    employee_number: "",
    api: "",
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Update form data when employee changes
  useEffect(() => {
    setFormData({
      id: employee.id,
      first_name: employee.first_name || "",
      last_name: employee.last_name || "",
      email: employee.email || "",
      phone: employee.phone || "",
      password: "", // Do not prepopulate password
      employee_number: employee.employee_number || "",
      has_driving_license: (employee as any).has_driving_license || false,
      created_at: employee.created_at,
    });
    setErrors({
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      employee_number: "",
      api: "",
    });
  }, [employee]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "", api: "" }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, has_driving_license: checked }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      employee_number: "",
      api: "",
    };
    if (!formData.first_name.trim()) {
      newErrors.first_name = "First name is required";
      isValid = false;
    }
    if (!formData.last_name.trim()) {
      newErrors.last_name = "Last name is required";
      isValid = false;
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }
    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
      isValid = false;
    }
    if (formData.password && formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
      isValid = false;
    }
    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setLoading(true);
    setErrors((prev) => ({ ...prev, api: "" }));
    try {
      // Send separate first_name and last_name like in create dialog
      const payload: any = {};
      if (formData.first_name.trim() !== employee.first_name)
        payload.first_name = formData.first_name.trim();
      if (formData.last_name.trim() !== employee.last_name)
        payload.last_name = formData.last_name.trim();
      if (formData.email !== employee.email) payload.email = formData.email;
      if (formData.phone !== employee.phone)
        payload.phone_number = formData.phone;
      if (formData.password) payload.password = formData.password;
      if (formData.employee_number !== employee.employee_number)
        payload.employee_number = formData.employee_number;
      if (
        formData.has_driving_license !== (employee as any).has_driving_license
      )
        payload.has_driving_license = formData.has_driving_license;
      if (Object.keys(payload).length === 0) {
        setErrors((prev) => ({ ...prev, api: "No changes to save." }));
        setLoading(false);
        return;
      }

      const updated = await api.employees.update(
        employee.id.toString(),
        payload
      );

      // Pass the updated employee data to the parent component with separate name fields
      onSubmit({
        id: employee.id,
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        email: formData.email,
        phone_number: formData.phone,
        employee_number: formData.employee_number,
        has_driving_license: formData.has_driving_license,
        company_id: employee.company_id,
        created_at: employee.created_at,
      });

      onOpenChange(false);
    } catch (err: any) {
      setErrors((prev) => ({
        ...prev,
        api: err.message || "Failed to update employee",
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Employee</DialogTitle>
          <DialogDescription>
            Update the employee details below.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-first_name">First Name</Label>
              <Input
                id="edit-first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                placeholder="John"
              />
              {errors.first_name && (
                <p className="text-sm text-red-500">{errors.first_name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-last_name">Last Name</Label>
              <Input
                id="edit-last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Doe"
              />
              {errors.last_name && (
                <p className="text-sm text-red-500">{errors.last_name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-phone">Phone Number</Label>
              <Input
                id="edit-phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="************"
              />
              {errors.phone && (
                <p className="text-sm text-red-500">{errors.phone}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-password">Password</Label>
              <div className="relative">
                <Input
                  id="edit-password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Leave blank to keep current password"
                  className="pr-10"
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword((prev) => !prev)}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-employee_number">Personal Number</Label>
              <Input
                id="edit-employee_number"
                name="employee_number"
                value={formData.employee_number}
                onChange={handleChange}
                placeholder="Enter personal number"
              />
              {errors.employee_number && (
                <p className="text-sm text-red-500">{errors.employee_number}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-has_driving_license"
                checked={formData.has_driving_license}
                onCheckedChange={handleCheckboxChange}
              />
              <Label
                htmlFor="edit-has_driving_license"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Has driving license
              </Label>
            </div>
            {errors.api && <p className="text-sm text-red-500">{errors.api}</p>}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
