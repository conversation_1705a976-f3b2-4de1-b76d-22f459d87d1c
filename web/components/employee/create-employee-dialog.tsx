"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Employee } from "@/types/employee";
import { useAuth } from "@/components/auth/auth-provider";
import { toast } from "sonner";
import { Loader2, Eye, EyeOff } from "lucide-react";

interface CreateEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (employee: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    password: string;
    company_id: string;
    employee_number?: string;
    has_driving_license: boolean;
  }) => void;
}

export function CreateEmployeeDialog({
  open,
  onOpenChange,
  onSubmit,
}: CreateEmployeeDialogProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    password: "",
    company_id: user?.company_id || "",
    employee_number: "",
    has_driving_license: false,
  });

  // Add a loading state
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Update company_id when user changes
  useEffect(() => {
    console.log("Current user:", user);
    if (user?.company_id) {
      console.log("Setting company_id from user:", user.company_id);
      setFormData((prev) => ({
        ...prev,
        company_id: user.company_id!,
      }));
    }
  }, [user?.company_id]);

  const [errors, setErrors] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    password: "",
    company_id: "",
    employee_number: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // Clear error when user types
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, has_driving_license: checked }));
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      password: "",
      company_id: "",
      employee_number: "",
    };

    if (!formData.first_name.trim()) {
      newErrors.first_name = "First name is required";
      isValid = false;
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = "Last name is required";
      isValid = false;
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }

    if (!formData.phone_number.trim()) {
      newErrors.phone_number = "Phone number is required";
      isValid = false;
    }

    if (!formData.password.trim()) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
      isValid = false;
    }

    if (!formData.company_id) {
      newErrors.company_id = "Company ID is required";
      isValid = false;
    }

    console.log("Validation errors:", newErrors);
    setErrors(newErrors);
    return isValid;
  };

  const handleButtonClick = () => {
    console.log("Submit button clicked");
    console.log("Current form data:", formData);
    if (validateForm()) {
      console.log("Form is valid, calling onSubmit");
      setLoading(true);
      // Combine first_name and last_name into name for the API
      const submitData = {
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        email: formData.email,
        phone_number: formData.phone_number,
        password: formData.password,
        company_id: formData.company_id,
        employee_number: formData.employee_number,
        has_driving_license: formData.has_driving_license,
      };
      onSubmit(submitData);
      setLoading(false);
      // Reset form
      setFormData({
        first_name: "",
        last_name: "",
        email: "",
        phone_number: "",
        password: "",
        company_id: user?.company_id || "",
        employee_number: "",
        has_driving_license: false,
      });
      // Clear errors
      setErrors({
        first_name: "",
        last_name: "",
        email: "",
        phone_number: "",
        password: "",
        company_id: "",
        employee_number: "",
      });
    } else {
      console.log("Form validation failed");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Employee</DialogTitle>
          <DialogDescription>
            Enter the employee details below to create a new employee account.
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleButtonClick();
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="first_name">First Name</Label>
              <Input
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                placeholder="John"
                onClick={(e) => e.stopPropagation()}
              />
              {errors.first_name && (
                <p className="text-sm text-red-500 mt-1">{errors.first_name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="last_name">Last Name</Label>
              <Input
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Doe"
                onClick={(e) => e.stopPropagation()}
              />
              {errors.last_name && (
                <p className="text-sm text-red-500 mt-1">{errors.last_name}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                onClick={(e) => e.stopPropagation()}
              />
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                name="phone_number"
                value={formData.phone_number}
                onChange={handleChange}
                placeholder="************"
                onClick={(e) => e.stopPropagation()}
              />
              {errors.phone_number && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.phone_number}
                </p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="••••••••"
                  onClick={(e) => e.stopPropagation()}
                  className="pr-10"
                />
                <button
                  type="button"
                  tabIndex={-1}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword((prev) => !prev)}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password}</p>
              )}
            </div>
            <div className="grid gap-2">
              <Label htmlFor="employee_number">
                Personal Number (Optional)
              </Label>
              <Input
                id="employee_number"
                name="employee_number"
                value={formData.employee_number}
                onChange={handleChange}
                placeholder="Enter personal number"
                onClick={(e) => e.stopPropagation()}
              />
              {errors.employee_number && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.employee_number}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="has_driving_license"
                checked={formData.has_driving_license}
                onCheckedChange={handleCheckboxChange}
              />
              <Label
                htmlFor="has_driving_license"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Has driving license
              </Label>
            </div>
            {errors.company_id && (
              <div className="text-sm text-red-500 mt-1">
                {errors.company_id}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Employee"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
