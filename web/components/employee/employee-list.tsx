"use client";

import { useState, useEffect } from "react";
import { DashboardSidebar } from "@/components/dashboard-sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Search } from "lucide-react";
import { EmployeeTable } from "./employee-table";
import { CreateEmployeeDialog } from "./create-employee-dialog";
import { EditEmployeeDialog } from "./edit-employee-dialog";
import { ViewEmployeeDialog } from "./view-employee-dialog";
import { DeleteEmployeeDialog } from "./delete-employee-dialog";
import { TopBar } from "@/components/top-bar";
import { api } from "@/lib/api";
import { Employee } from "@/types/employee";
import { toast } from "sonner";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

export function EmployeeList() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(
    null
  );
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  // Removed useToast hook

  const fetchEmployees = async () => {
    try {
      const data = await api.employees.list();
      setEmployees(data.employees);
    } catch (err) {
      toast.error("Failed to fetch employees. Please try again.");
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, [toast]);

  // Filter employees based on search query
  const filteredEmployees = employees.filter(
    (employee) =>
      (employee.name?.toLowerCase().includes(searchQuery.toLowerCase()) ??
        false) ||
      (employee.phone?.includes(searchQuery) ?? false) ||
      (employee.employee_number
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ??
        false)
  );

  // Calculate pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentEmployees = filteredEmployees.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredEmployees.length / itemsPerPage);

  // Handle page navigation
  const handlePrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // Handle create employee
  const handleCreateEmployee = async (newEmployee: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    password: string;
    company_id: string;
    employee_number?: string;
    has_driving_license: boolean;
  }) => {
    console.log("handleCreateEmployee called with:", newEmployee);
    try {
      console.log("Calling API to create employee");
      const createdEmployee = await api.employees.create(newEmployee);
      console.log("Employee created successfully:", createdEmployee);
      setEmployees((prev) => [...prev, createdEmployee]);
      toast.success("Employee created successfully");
      setIsCreateDialogOpen(false);
    } catch (err) {
      console.error("Error creating employee:", err);
      toast.error("Failed to create employee. Please try again.");
    }
  };

  // Handle edit employee
  const handleEditEmployee = async (updatedEmployee: {
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    password?: string;
    employee_number?: string;
    has_driving_license?: boolean;
    id: number;
    company_id: number;
    created_at: string;
  }) => {
    try {
      // Convert the separate name fields back to Employee format for local state
      const employeeForState: Employee = {
        id: updatedEmployee.id,
        first_name: updatedEmployee.first_name,
        last_name: updatedEmployee.last_name,
        name: `${updatedEmployee.first_name} ${updatedEmployee.last_name}`.trim(),
        email: updatedEmployee.email,
        phone: updatedEmployee.phone_number,
        employee_number: updatedEmployee.employee_number || null,
        company_id: updatedEmployee.company_id,
        created_at: updatedEmployee.created_at,
      };

      // First update the local state for immediate feedback
      setEmployees(
        employees.map((employee) =>
          employee.id === updatedEmployee.id ? employeeForState : employee
        )
      );

      // Then refetch from the API to ensure data consistency
      await fetchEmployees();

      toast.success("Employee updated successfully");
    } catch (err) {
      toast.error("Failed to update employee. Please try again.");
    }
  };

  // Handle delete employee
  const handleDeleteEmployee = async (id: number) => {
    try {
      await api.employees.delete(id.toString());

      // Remove the deleted employee from the state
      setEmployees((prev) => prev.filter((emp) => emp.id !== id));

      toast.success("Employee deleted successfully");
      setIsDeleteDialogOpen(false);
    } catch (err: any) {
      console.error("Error deleting employee:", err);
      toast.error(err.message);
    }
  };

  // Handle view employee
  const handleViewEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsViewDialogOpen(true);
  };

  // Handle edit button click
  const handleEditClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsDeleteDialogOpen(true);
  };

  return (
    <>
      <DashboardSidebar />
      <div className="flex-1 overflow-auto h-screen">
        <TopBar title="Employee Management" />
        <main className="p-6 pb-24">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search employees..."
                className="w-full pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Employee
            </Button>
          </div>

          <EmployeeTable
            employees={currentEmployees}
            onView={handleViewEmployee}
            onEdit={handleEditClick}
            onDelete={handleDeleteClick}
          />

          {/* Pagination */}
          {filteredEmployees.length > itemsPerPage && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {indexOfFirstItem + 1}-
                {Math.min(indexOfLastItem, filteredEmployees.length)} of{" "}
                {filteredEmployees.length} employees
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={handlePrevPage}
                      className={
                        currentPage === 1
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                    (page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={page === currentPage}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    )
                  )}
                  <PaginationItem>
                    <PaginationNext
                      onClick={handleNextPage}
                      className={
                        currentPage === totalPages
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}

          {/* Create Employee Dialog */}
          <CreateEmployeeDialog
            open={isCreateDialogOpen}
            onOpenChange={setIsCreateDialogOpen}
            onSubmit={handleCreateEmployee}
          />

          {/* Edit Employee Dialog */}
          {selectedEmployee && (
            <EditEmployeeDialog
              open={isEditDialogOpen}
              onOpenChange={setIsEditDialogOpen}
              employee={selectedEmployee}
              onSubmit={handleEditEmployee}
            />
          )}

          {/* View Employee Dialog */}
          {selectedEmployee && (
            <ViewEmployeeDialog
              open={isViewDialogOpen}
              onOpenChange={setIsViewDialogOpen}
              employee={selectedEmployee}
            />
          )}

          {/* Delete Employee Dialog */}
          {selectedEmployee && (
            <DeleteEmployeeDialog
              open={isDeleteDialogOpen}
              onOpenChange={setIsDeleteDialogOpen}
              employee={selectedEmployee}
              onConfirm={() => handleDeleteEmployee(selectedEmployee.id)}
            />
          )}
        </main>
      </div>
    </>
  );
}
