"use client";

import { useState } from "react";
import {
  Banknote,
  Calendar,
  Clock,
  DollarSign,
  RefreshCw,
  Users,
} from "lucide-react";
import { TopBar } from "@/components/top-bar";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function DashboardContent() {
  const [period, setPeriod] = useState("this-month");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  // Sample recent activity data
  const recentActivity = [
    {
      id: 1,
      user: "<PERSON>",
      action: "submitted timesheet",
      time: "10 minutes ago",
    },
    {
      id: 2,
      user: "<PERSON>",
      action: "approved vacation request",
      time: "1 hour ago",
    },
    {
      id: 3,
      user: "Admin",
      action: "processed payroll",
      time: "3 hours ago",
    },
    {
      id: 4,
      user: "<PERSON>",
      action: "updated personal information",
      time: "Yesterday",
    },
    {
      id: 5,
      user: "David <PERSON>",
      action: "requested overtime approval",
      time: "Yesterday",
    },
  ];

  // Sample pending items
  const pendingItems = [
    {
      id: 1,
      type: "Timesheet Approval",
      user: "<PERSON> <PERSON>",
    },
    {
      id: 2,
      type: "Vacation Request",
      user: "Michael Chen",
    },
    {
      id: 3,
      type: "Overtime Approval",
      user: "David Kim",
    },
  ];

  return (
    <div className="flex-1 flex flex-col">
      <TopBar title="Dashboard" />
      <div className="flex-1 p-6 bg-gray-50 overflow-auto">
        <div className="max-w-7xl mx-auto">
          {/* Dashboard Header */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">
                Company Dashboard
              </h1>
              <p className="text-muted-foreground">
                Overview of your company's performance
              </p>
            </div>
            <div className="flex items-center gap-2 mt-4 md:mt-0">
              <Select value={period} onValueChange={setPeriod}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="this-week">This Week</SelectItem>
                  <SelectItem value="this-month">This Month</SelectItem>
                  <SelectItem value="last-month">Last Month</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" onClick={handleRefresh}>
                <RefreshCw
                  className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                />
              </Button>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Employees
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">124</div>
                <div className="text-xs text-muted-foreground">
                  4 new this month
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending Timesheets
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">18</div>
                <div className="text-xs text-muted-foreground">
                  Needs approval
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Monthly Payroll
                </CardTitle>
                <Banknote className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">SEK 142,568</div>
                <div className="text-xs text-muted-foreground">
                  Due in 8 days
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Overtime Hours
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">284.5</div>
                <div className="text-xs text-muted-foreground">
                  12% from last month
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Activity */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-start pb-4 border-b last:border-0 last:pb-0"
                    >
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex-shrink-0" />
                      <div className="ml-4">
                        <p className="font-medium">
                          {item.user} {item.action}
                        </p>
                        <p className="text-sm text-gray-500">{item.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Pending Items */}
            <Card>
              <CardHeader>
                <CardTitle>Pending Approvals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pendingItems.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center justify-between p-2 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">{item.type}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.user}
                        </div>
                      </div>
                      <Button size="sm">Review</Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Events */}
          <div className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center p-2 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium">Mid-Month Payroll</div>
                      <div className="text-sm text-muted-foreground">
                        May 15, 2025
                      </div>
                    </div>
                    <div className="ml-auto text-sm">8 days</div>
                  </div>
                  <div className="flex items-center p-2 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium">End-Month Payroll</div>
                      <div className="text-sm text-muted-foreground">
                        May 31, 2025
                      </div>
                    </div>
                    <div className="ml-auto text-sm">24 days</div>
                  </div>
                  <div className="flex items-center p-2 bg-muted/50 rounded-lg">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                      <Calendar className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium">Tax Filing</div>
                      <div className="text-sm text-muted-foreground">
                        June 15, 2025
                      </div>
                    </div>
                    <div className="ml-auto text-sm">39 days</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
