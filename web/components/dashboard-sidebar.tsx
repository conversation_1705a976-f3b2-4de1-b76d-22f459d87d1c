"use client";

import {
  Calendar,
  ClipboardList,
  FileText,
  LayoutGrid,
  Settings,
  Users,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

export function DashboardSidebar() {
  const pathname = usePathname();

  const categories = [
    {
      name: "Company",
      items: [
        // {
        //   title: "Dashboard",
        //   icon: LayoutGrid,
        //   href: "/",
        //   isActive: pathname === "/",
        // },
        {
          title: "Employee",
          icon: Users,
          href: "/employee",
          isActive: pathname === "/employee",
        },
        {
          title: "Logs",
          icon: ClipboardList,
          href: "/logs",
          isActive: pathname === "/logs",
        },
        {
          title: "Timesheet",
          icon: FileText,
          href: "/timesheet",
          isActive: pathname === "/timesheet",
        },
        {
          title: "Schedule",
          icon: Calendar,
          href: "/schedule",
          isActive: pathname === "/schedule",
        },
        {
          title: "Payroll Settings",
          icon: Settings,
          href: "/settings",
          isActive: pathname === "/settings",
        },
      ],
    },
  ];

  return (
    <div className="w-64 h-screen border-r bg-white flex flex-col sticky top-0 left-0">
      <div className="h-16 border-b flex items-center px-6">
        <Link href="/" className="flex items-center">
          <Image
            src="/schedulo.svg"
            alt="Schedulo"
            width={126}
            height={27}
            className="h-6 w-auto"
          />
        </Link>
      </div>
      <div className="flex-1 py-6">
        {categories.map((category) => (
          <div key={category.name} className="mb-6">
            <div className="px-6 mb-2 text-gray-500 font-medium">
              {category.name}
            </div>
            <nav className="space-y-1">
              {category.items.map((item) => (
                <Link
                  key={item.title}
                  href={item.href}
                  className={`
                    flex items-center px-6 py-2.5 text-sm font-medium
                    ${item.isActive ? "bg-gray-100" : "hover:bg-gray-50"}
                  `}
                >
                  <item.icon className="h-5 w-5 mr-3" />
                  {item.title}
                </Link>
              ))}
            </nav>
          </div>
        ))}
      </div>
    </div>
  );
}
