# Payroll management software
This is an application that helps companies track the time of their employee check-in check-out and thus manage their payroll. It has two components. One is web dashboard built in React which will be used by the company to manage their employees, their timesheets and payment reports. Next is a server rest api written in NestJS.

For NestJS we should be using Supabase for authentication, database, file storage etc. Supabase credentials can be stored in the .env file.

# Functionality
1. Employees will login through a flutter mobile app (which we will develop separately) to check-in, check-out and view their payment reports.
2. Company admin will login to web dashboard to manage their employees (Add/edit/update/delete), view, approve their timesheets etc
3. Superadmin can manage the companies (but this functionality we will skip for now)

# Instructions

1. The web frontend is generated using V0 (an AI based frontend builder that uses shadcn and nextjs). It contains the frontend code, but without API integration.
2. You must write the REST APIs in NestJS and integrate it with the frontend.
3. In NestJS, for auth, you must follow whatever is the right method to do the auth via supabase.
4. In NestJS, db should be the postgres db from supabase.
5. In NestJS, Use Prisma as the ORM
6. In NestJS, while writing api end points, make sure to follow the standard structure using controllers, services, dtos etc.
7. Code must be readable and workable for other developers. So make sure to add comments that any developer can understand.
8. Understand the folder structure of the frontend(web) app. You might be familiar as it is nextjs.

# Users
1. This app has three different levels of users - super_admin, company_admin, employee

# API Standards

## Validation
1. All API endpoints must use proper validation using class-validator decorators
2. Validation errors should return 400 Bad Request with clear error messages
3. DTOs must include:
   - Proper validation decorators (e.g., @IsEmail(), @MinLength(), @IsEnum())
   - Clear error messages for each validation rule
   - Swagger documentation using @ApiProperty()
4. Global validation pipe is configured to:
   - Strip properties that don't have decorators (whitelist: true)
   - Transform payloads to DTO instances (transform: true)
   - Throw errors for non-whitelisted properties (forbidNonWhitelisted: true)
   - Automatically convert primitive types (enableImplicitConversion: true)

## Error Handling
1. All API endpoints must handle errors properly and return appropriate HTTP status codes
2. Common status codes:
   - 200: Success
   - 201: Created
   - 400: Bad Request (validation errors)
   - 401: Unauthorized
   - 403: Forbidden
   - 404: Not Found
   - 500: Internal Server Error (only for unexpected server errors)
3. Error responses should include:
   - Clear error message
   - Error code (if applicable)
   - Validation errors (if any)

## Swagger Documentation
1. All API endpoints must be documented using Swagger/OpenAPI decorators
2. Controllers must include:
   - @ApiTags() for grouping endpoints
   - @ApiBearerAuth() for authenticated endpoints
   - @ApiOperation() with summary and description
   - @ApiResponse() for all possible response types
3. DTOs must include:
   - @ApiProperty() for all properties with descriptions
   - Proper type definitions for Swagger
4. Update Swagger documentation whenever an API endpoint is modified
5. Include authentication requirements in the documentation
6. Document all possible error responses
7. Use descriptive summaries and descriptions for better API understanding