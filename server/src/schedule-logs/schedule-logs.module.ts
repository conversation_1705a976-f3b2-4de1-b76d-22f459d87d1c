import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleLogsService } from './schedule-logs.service';
import { ScheduleLogsController } from './schedule-logs.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { ScheduleLogsUtilsService } from './services/schedule-logs-utils.service';
import { PrismaService } from 'src/prisma/prisma.service';

@Module({
  imports: [PrismaModule, SupabaseModule],
  controllers: [ScheduleLogsController],
  providers: [ScheduleLogsService,ScheduleLogsUtilsService,PrismaService],
})
export class ScheduleLogsModule { }
