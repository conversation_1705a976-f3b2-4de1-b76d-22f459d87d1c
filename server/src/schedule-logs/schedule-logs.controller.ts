import {
  Controller, Post, Body, BadRequestException, Get, Query, Param, Patch, UseGuards,
  Req,
  Request
} from '@nestjs/common';
import {
  ApiTags, ApiBearerAuth, ApiOperation, ApiCreatedResponse, ApiOkResponse, ApiBadRequestResponse,
  ApiNotFoundResponse, ApiParam, ApiBody,
  ApiResponse
} from '@nestjs/swagger';
import { JwtAuthGuard, RequestWithUser } from '../auth/jwt-auth.guard';
import { ScheduleLog } from './entities/schedule-log.entity';
import { ScheduleLogsService } from './schedule-logs.service';
import { ClockInDto } from './dto/clock-in.dto';
import { ClockOutDto } from './dto/clock-out.dto';
import { ListScheduleLogsDto } from './dto/list-schedule-logs.dto';
import { ScheduleLogAdminActionDto } from './dto/schedule-log-admin-action.dto';
import { ScheduleLogResponseDto } from './dto/schedule-log-response.dto';
import { PaginateResponseDto } from 'src/common/dto/paginate.dto';
import { EditScheduleLogTimesDto } from './dto/edit-schedule-log-times.dto';
import {
  scheduleLogClockInExample,
  scheduleLogClockOutExample,
  scheduleLogListAdminExample,
  scheduleLogListEmployeeExample,
  scheduleLogViewExample,
  scheduleLogApproveExample,
  scheduleLogRejectExample,
  scheduleLogEditTimesExample,
} from './docs/schedule-log-swagger.examples';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { ScheduleLogListAdminResponseDto } from './dto/schedule-log-list.dto';

@ApiTags('Schedule Logs')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('schedule-logs')
export class ScheduleLogsController {
  constructor(private readonly scheduleLogsService: ScheduleLogsService) { }

  @Post('clock-in')
  @ApiOperation({ summary: 'Clock in', description: 'Clock in to a schedule.' })
  @ApiCreatedResponse({
    description: 'Clocked in successfully.',
    schema: { example: scheduleLogClockInExample },
  })
  @ApiBadRequestResponse({ description: 'Already clocked in or schedule already exists for this date, or employee has an open schedule log.' })
  async clockIn(@Body() dto: ClockInDto) {
    const scheduleExists = await this.scheduleLogsService.scheduleExists(dto.employee_id, dto.schedule_id);
    if (scheduleExists) {
      throw new BadRequestException('Schedule already exists for this date.');
    }
    const hasOpen = await this.scheduleLogsService.hasOpenSchedule(dto.employee_id);
    if (hasOpen) {
      throw new BadRequestException('Employee has an open schedule log.');
    }
    return this.scheduleLogsService.clockIn(dto);
  }

  @Post('clock-in/admin')
  @ApiOperation({ summary: 'Clock in', description: 'Clock in to a schedule.' })
  @ApiCreatedResponse({
    description: 'Clocked in successfully.',
    schema: { example: scheduleLogClockInExample },
  })
  @ApiBadRequestResponse({ description: 'Already clocked in or schedule already exists for this date, or employee has an open schedule log.' })
  async clockInAdmin(@Body() dto: ClockInDto) {
    const scheduleExists = await this.scheduleLogsService.scheduleExists(dto.employee_id, dto.schedule_id);
    if (scheduleExists) {
      throw new BadRequestException('Schedule already exists for this date.');
    }
    // const hasOpen = await this.scheduleLogsService.hasOpenSchedule(dto.employee_id);
    // if (hasOpen) {
    //   throw new BadRequestException('Employee has an open schedule log.');
    // }
    return this.scheduleLogsService.clockIn(dto);
  }

  @Post('clock-out')
  @ApiOperation({ summary: 'Clock out', description: 'Clock out of a schedule.' })
  @ApiOkResponse({
    description: 'Clocked out successfully.',
    schema: { example: scheduleLogClockOutExample },
  })
  @ApiBadRequestResponse({ description: 'Schedule log not found or already clocked out or not clocked in.' })
  async clockOut(@Body() dto: ClockOutDto) {
    return this.scheduleLogsService.clockOut(dto);
  }

  @Get('admin')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({ summary: 'List schedule logs (admin)', description: 'List all schedule logs with filters and pagination.' })
  @ApiOkResponse({
    description: 'Returns a list of schedule logs',
    type: ScheduleLogListAdminResponseDto,
    schema: { example: scheduleLogListAdminExample },
  })
  async listLogs(@Request() req, @Query() query: ListScheduleLogsDto) {
    query.company_id = Number(req.user.company_id);
    return this.scheduleLogsService.listLogs(query);
  }

  @Get('employee')
  @ApiOperation({ summary: 'List schedule logs (employee)', description: 'List schedule logs for an employee with filters and pagination.' })
  @ApiOkResponse({
    description: 'List of schedule logs.',
    type: PaginateResponseDto,
    schema: { example: scheduleLogListEmployeeExample },
  })
  @ApiBadRequestResponse({ description: 'Employee id is required.' })
  async listLogsEmployee(@Query() query: ListScheduleLogsDto) {
    if (!query.employee_id) {
      throw new BadRequestException('Employee id is required.');
    }
    return this.scheduleLogsService.listLogsEmployee(query);
  }

  @Get('stats')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({
    summary: 'Get status-wise counts and total duration sum',
    description: 'Returns count for each status and the sum of durations for all logs, with optional filters.',
  })
  @ApiOkResponse({
    description: 'Status-wise counts and total duration sum.',
    schema: {
      example: {
        counts: {
          pending: 5,
          approved: 10,
          rejected: 2,
          paid: 3,
        },
        total_duration: 4700,
      },
    },
  })
  async getStatusStats(@Request() req, @Query() query: { company_id?: number; employee_id?: string; start_date?: string; end_date?: string; created_by?: string }) {
    return this.scheduleLogsService.getStatusStats(query, req.user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'View schedule log', description: 'Get a schedule log by its ID.' })
  @ApiParam({ name: 'id', type: Number })
  @ApiOkResponse({
    description: 'Schedule log found.',
    schema: { example: scheduleLogViewExample },
  })
  @ApiNotFoundResponse({ description: 'Schedule log not found.' })
  async view(@Param('id') id: number) {
    return this.scheduleLogsService.viewLog(id);
  }

  @Patch(':id/approve')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({
    summary: 'Approve a schedule log',
    description: 'Approve a schedule log by ID. Only company admins and super admins can approve logs.',
  })
  @ApiParam({ name: 'id', type: Number })
  @ApiBody({ type: ScheduleLogAdminActionDto })
  @ApiOkResponse({
    description: 'Schedule log approved successfully.',
    type: ScheduleLogResponseDto,
    schema: { example: scheduleLogApproveExample },
  })
  @ApiBadRequestResponse({ description: 'Already approved or cannot approve.' })
  @ApiNotFoundResponse({ description: 'Schedule log not found.' })
  async accept(
    @Param('id') id: number,
    @Body() dto: ScheduleLogAdminActionDto,
  ): Promise<ScheduleLogResponseDto> {
    return this.scheduleLogsService.acceptLog(id, dto);
  }

  @Patch(':id/reject')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({
    summary: 'Reject a schedule log',
    description: 'Reject a schedule log by ID. Only company admins and super admins can reject logs.',
  })
  @ApiParam({ name: 'id', type: Number })
  @ApiBody({ type: ScheduleLogAdminActionDto })
  @ApiOkResponse({
    description: 'Schedule log rejected successfully.',
    type: ScheduleLogResponseDto,
    schema: { example: scheduleLogRejectExample },
  })
  @ApiBadRequestResponse({ description: 'Already rejected or cannot reject.' })
  @ApiNotFoundResponse({ description: 'Schedule log not found.' })
  async reject(
    @Param('id') id: number,
    @Body() dto: ScheduleLogAdminActionDto,
  ): Promise<ScheduleLogResponseDto> {
    return this.scheduleLogsService.rejectLog(id, dto);
  }

  @Patch(':id/edit-times')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({
    summary: 'Edit schedule log times',
    description: 'Edit clock in, clock out, break start, and break end times of a schedule log. Only company admins and super admins can edit times.',
  })
  // @ApiParam({ name: 'id', type: Number })
  @ApiBody({ type: EditScheduleLogTimesDto })
  @ApiOkResponse({
    description: 'Schedule log times updated successfully.',
    type: ScheduleLog,
    schema: { example: scheduleLogEditTimesExample }
  })
  @ApiBadRequestResponse({
    description: 'Invalid time values or break time constraints.'
  })
  @ApiNotFoundResponse({ description: 'Schedule log not found.' })
  async editTimes(
    @Param('id') id: number,
    @Body() dto: EditScheduleLogTimesDto
  ): Promise<ScheduleLog> {
    dto.id = id;
    return this.scheduleLogsService.editScheduleLogTimes(dto);
  }
}
