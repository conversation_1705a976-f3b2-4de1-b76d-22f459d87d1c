export const scheduleLogClockInExample = {
  id: 1,
  employee_id: 'emp_123',
  schedule_id: 2,
  company_id: 'comp_1',
  clock_in_at: '2025-05-21T09:00:00.000Z',
  note: null,
  status: 'started',
};

export const scheduleLogClockOutExample = {
  id: 1,
  employee_id: 'emp_123',
  schedule_id: 2,
  company_id: 1,
  clock_in_at: '2025-05-21T09:00:00.000Z',
  clock_out_at: '2025-05-21T17:00:00.000Z',
  duration: 480,
  note: null,
  status: 'pending',
};

export const scheduleLogListAdminExample = {
  paginate: {
    page: 1,
    limit: 10,
    total_data: 2,
    total_pages: 1,
  },
  data: [
    {
      "id": 24,
      "schedule_id": 86,
      "company_id": 1,
      "employee_id": "9c853ca6-2571-4132-90b4-57fbf3555470",
      "status": "approved",
      "clock_in_at": "2025-06-04T10:30:00.000Z",
      "clock_out_at": "2025-06-04T18:30:00.000Z",
      "note": "nothings",
      "admin_note": null,
      "duration": 480,
      "created_at": "2025-05-29T14:08:48.394Z",
      "approved_at": null,
      "work_duration": 480,
      "overtime_duration": 0,
      "total_pay": "1693.82",
      "overtime_pay": "0",
      "work_pay": "1693.82",
      "time_sheet_updated": false,
      "time_sheet_id": null,
      "break_time": 0,
      employee: {
        id: 'emp_123',
        name: 'John Doe',
        phone: '1234567890',
      },
      "work_pay_split": [
        {
          "end": "2025-06-04T18:00:00.000+05:30",
          "rate": 181.45,
          "shift": {
            "id": 1,
            "name": "Normal Shift",
            "end_time": "18:00",
            "break_time": 60,
            "start_time": "06:00",
            "hourly_rate": 181.45
          },
          "start": "2025-06-04T16:00:00.000+05:30",
          "amount": 362.9,
          "hours_worked": 2
        },
      ],
      "overtime_pay_split": [
        {
          "end": "2025-06-04T23:00:00.000+05:30",
          "rate": 216.78,
          "shift": {
            "id": 2,
            "name": "OB1",
            "end_time": "23:00",
            "break_time": 60,
            "start_time": "18:00",
            "hourly_rate": 216.78
          },
          "start": "2025-06-04T18:00:00.000+05:30",
          "amount": 1083.9,
          "hours_worked": 5
        },
      ]
    }
  ],
};

export const scheduleLogListEmployeeExample = {
  paginate: {
    page: 1,
    limit: 10,
    total_data: 1,
    total_pages: 1,
  },
  data: [
    {
      id: 1,
      employee_id: 'emp_123',
      schedule_id: 2,
      company_id: 1,
      clock_in_at: '2025-05-21T09:00:00.000Z',
      clock_out_at: '2025-05-21T17:00:00.000Z',
      duration: 480,
      note: null,
      status: 'pending',
    },
  ],
};

export const scheduleLogViewExample = {
  id: 1,
  employee_id: 'emp_123',
  schedule_id: 2,
  company_id: 1,
  clock_in_at: '2025-05-21T09:00:00.000Z',
  clock_out_at: '2025-05-21T17:00:00.000Z',
  duration: 480,
  note: null,
  status: 'pending',
  employee: {
    id: 'emp_123',
    name: 'John Doe',
    phone: '1234567890',
  },
  schedules: {
    id: 2,
    start_date: '2025-05-21',
    end_date: '2025-05-21',
    start_time: '09:00',
    end_time: '17:00',
  },
};

export const scheduleLogApproveExample = {
  id: 1,
  status: 'approved',
  approved_at: '2025-05-21T12:00:00.000Z',
  admin_note: 'Approved by admin',
};

export const scheduleLogRejectExample = {
  id: 1,
  status: 'rejected',
  admin_note: 'Rejected due to missing info',
};

export const scheduleLogEditTimesExample = {
  id: 1,
  employee_id: 'emp_123',
  company_id: 1,
  schedule_id: 2,
  status: 'started',
  clock_in_at: '2025-05-21T09:30:00.000Z',  // Updated clock in time
  clock_out_at: '2025-05-21T18:30:00.000Z',  // Updated clock out time
  break_start: '2025-05-21T13:00:00.000Z',   // Updated break start
  break_end: '2025-05-21T14:00:00.000Z',     // Updated break end
  break_time: 60,                             // Break duration in minutes
  duration: 540,                              // Total duration in minutes
  work_duration: 480,                         // Work duration minus break
  note: 'Times adjusted by admin',
  admin_note: null,
  created_at: '2025-05-21T09:30:00.000Z',
  approved_at: null,
  time_sheet_updated: false,
  time_sheet_id: null
};