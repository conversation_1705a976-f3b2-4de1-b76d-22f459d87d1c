import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { schedule_log_status } from '@prisma/client';

interface TimeSlot {
  id: number;
  hourly_rate: number;
  start_time: string; // "HH:mm"
  end_time: string;   // "HH:mm"
  break_time?: number;
  name: string;
}

interface ShiftBreakdown {
  shift: TimeSlot;
  start: string;
  end: string;
  hours_worked: number;
  rate: number;
  amount: number;
  break_time: number;
}

interface ShiftBreakdownResult {
  total: number;
  total_duration_minutes: number;
  breakdown: ShiftBreakdown[];
}

@Injectable()
export class ScheduleLogsUtilsService {
  constructor(private prisma: PrismaService) { }

  async calculateDurationAndAmounts(schedule_log_id: number) {
    const log = await this.prisma.schedule_logs.findUnique({
      where: { id: schedule_log_id },
      include: {
        schedules: {
          select: {
            end_date: true,
          },
        },
        company: {
          select: {
            extra_pay_rate: true,
          },
        },
        employee: {
          select: {
            has_driving_license: true,
          },
        },
      },
    });

    if (!log?.clock_in_at || !log?.clock_out_at) {
      throw new NotFoundException('Schedule log not found or missing clock times.');
    }

    const timeSlotsRaw = await this.prisma.time_slot_rates.findMany({
      where: { company_id: log.company_id },
      orderBy: { start_time: 'asc' },
    });

    const timeSlots: TimeSlot[] = timeSlotsRaw.map(slot => ({
      id: slot.id,
      hourly_rate: slot.hourly_rate !== null ? Number(slot.hourly_rate) : 0,
      break_time: slot.break_time ?? 0,
      name: slot.name ?? '',
      start_time: this.formatTimeFromDate(new Date(slot.start_time)),
      end_time: this.formatTimeFromDate(new Date(slot.end_time)),
    }));

    const clockInAt = new Date(log.clock_in_at);
    let clockOutAt = new Date(log.clock_out_at);
    let scheduleEnd = log?.schedules?.end_date ? new Date(log.schedules.end_date) : null;

    const isOvertime = scheduleEnd && clockOutAt > scheduleEnd;
    let breakTime = log.break_time;
    let status: schedule_log_status;

    let workResult: ShiftBreakdownResult = {
      total: 0,
      total_duration_minutes: 0,
      breakdown: [],
    };
    let overtimeResult: ShiftBreakdownResult = {
      total: 0,
      total_duration_minutes: 0,
      breakdown: [],
    };

    if (isOvertime) {

      workResult = await this.calculateShiftBreakdown(
        clockInAt,
        scheduleEnd,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      overtimeResult = await this.calculateShiftBreakdown(
        scheduleEnd,
        clockOutAt,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      status = schedule_log_status.pending;
    } else {
      workResult = await this.calculateShiftBreakdown(
        clockInAt,
        clockOutAt,
        timeSlots,
        log?.break_start ? new Date(log.break_start) : undefined,
        log.break_end ? new Date(log.break_end) : undefined
      );

      status = schedule_log_status.approved;
    }

    let totalPay = +(workResult.total + overtimeResult.total);
    const totalDuration = workResult.total_duration_minutes + overtimeResult.total_duration_minutes;
    let extra_pay = 0;

    if (log.employee?.has_driving_license == true) {
      const extraPay = log.company?.extra_pay_rate;
      extra_pay = (Number(totalDuration) / 60) * Number(extraPay);
      totalPay = Number(totalPay) + extra_pay;
    }

    const result = this.prisma.schedule_logs.update({
      where: { id: schedule_log_id },
      data: {
        total_pay: totalPay,
        work_pay: workResult.total,
        overtime_pay: overtimeResult.total,
        work_duration: workResult.total_duration_minutes,
        overtime_duration: overtimeResult.total_duration_minutes,
        duration: totalDuration,
        break_time: breakTime,
        status,
        work_pay_split: JSON.parse(JSON.stringify(workResult.breakdown)),
        overtime_pay_split: JSON.parse(JSON.stringify(overtimeResult.breakdown)),
        extra_pay
      },
    });

    await this.createScheduleLogTimeSlots(schedule_log_id, workResult.breakdown, overtimeResult.breakdown);
    return result;
  }

  private formatTimeFromDate(date: Date): string {
    return date.toTimeString().slice(0, 5); // "HH:mm"
  }

  private parseTime(timeStr: string): { hours: number; minutes: number } {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return { hours, minutes };
  }

  private addMinutesToDate(date: Date, minutes: number): Date {
    return new Date(date.getTime() + minutes * 60000);
  }

  private getMinutesDifference(start: Date, end: Date): number {
    return Math.floor((end.getTime() - start.getTime()) / 60000);
  }

  async calculateShiftBreakdown(
    clockIn: Date,
    clockOut: Date,
    shifts: TimeSlot[],
    breakStart?: Date,
    breakEnd?: Date,
  ): Promise<ShiftBreakdownResult> {
    const breakdown: ShiftBreakdown[] = [];
    let totalMinutes = 0;

    // Get the date range we need to check
    const startDate = new Date(clockIn);
    const endDate = new Date(clockOut);

    // Check each day from clock in to clock out
    let currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    while (currentDate <= endDateOnly) {
      for (const shift of shifts) {
        const { shiftStart, shiftEnd } = this.getShiftTimes(currentDate, shift);

        // Find overlap between work period and shift period
        const overlapStart = new Date(Math.max(clockIn.getTime(), shiftStart.getTime()));
        const overlapEnd = new Date(Math.min(clockOut.getTime(), shiftEnd.getTime()));

        if (overlapStart >= overlapEnd) continue; // No overlap

        let workMinutes = this.getMinutesDifference(overlapStart, overlapEnd);
        let breakMinutes = 0;

        // Calculate break time that overlaps with this shift period
        if (breakStart && breakEnd) {
          const breakOverlapStart = new Date(Math.max(overlapStart.getTime(), breakStart.getTime()));
          const breakOverlapEnd = new Date(Math.min(overlapEnd.getTime(), breakEnd.getTime()));

          if (breakOverlapStart < breakOverlapEnd) {
            breakMinutes = this.getMinutesDifference(breakOverlapStart, breakOverlapEnd);
            workMinutes -= breakMinutes;
          }
        }

        if (workMinutes <= 0) continue;

        const hoursWorked = workMinutes / 60;
        const amount = +(hoursWorked * shift.hourly_rate);

        breakdown.push({
          shift,
          start: overlapStart.toISOString(),
          end: overlapEnd.toISOString(),
          hours_worked: +(Math.trunc(hoursWorked * 100) / 100),
          rate: shift.hourly_rate,
          amount,
          break_time: breakMinutes,
        });

        totalMinutes += workMinutes;
      }

      // Move to next day
      currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
    }

    const total = breakdown.reduce((acc, item) => acc + item.amount, 0);

    return {
      total: +total,
      total_duration_minutes: totalMinutes,
      breakdown,
    };
  }

  private getShiftTimes(date: Date, shift: TimeSlot): { shiftStart: Date; shiftEnd: Date } {
    const startTime = this.parseTime(shift.start_time);
    const endTime = this.parseTime(shift.end_time);

    const shiftStart = new Date(date);
    shiftStart.setHours(startTime.hours, startTime.minutes, 0, 0);

    let shiftEnd = new Date(date);
    shiftEnd.setHours(endTime.hours, endTime.minutes, 0, 0);

    // Handle overnight shifts (end time is before start time)
    if (shift.end_time < shift.start_time) {
      shiftEnd = new Date(shiftEnd.getTime() + 24 * 60 * 60 * 1000); // Add one day
    }

    return { shiftStart, shiftEnd };
  }

  async createScheduleLogTimeSlots(
    schedule_log_id: number,
    workBreakdown: ShiftBreakdown[],
    overtimeBreakdown: ShiftBreakdown[]
  ) {
    await this.prisma.schedule_log_time_slots.deleteMany({
      where: {
        schedule_log_id
      }
    });
    const allBreakdowns = [...(workBreakdown || []), ...(overtimeBreakdown || [])];

    const toCreate = allBreakdowns
      .filter(b => b.amount > 0)
      .map(b => ({
        schedule_log_id,
        time_slot_rate_id: b.shift.id,
        break_time: b.break_time,
        hours_worked: b.hours_worked,
        start: b.start ? new Date(b.start) : undefined,
        end: b.end ? new Date(b.end) : undefined,
        rate: b.rate,
        amount: b.amount,
      }));

    if (toCreate.length === 0) return [];

    return this.prisma.schedule_log_time_slots.createMany({
      data: toCreate,
      skipDuplicates: true,
    });
  }
}