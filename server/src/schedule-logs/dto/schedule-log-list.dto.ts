import { ApiProperty } from '@nestjs/swagger';

class ScheduleLogEmployeeDto {
  @ApiProperty() id: string;
  @ApiProperty() name: string;
  @ApiProperty() phone: string;
}

class ScheduleLogShiftDto {
  @ApiProperty() id: number;
  @ApiProperty() name: string;
  @ApiProperty() end_time: string;
  @ApiProperty() break_time: number;
  @ApiProperty() start_time: string;
  @ApiProperty() hourly_rate: number;
}

class ScheduleLogPaySplitDto {
  @ApiProperty() start: string;
  @ApiProperty() end: string;
  @ApiProperty() rate: number;
  @ApiProperty({ type: ScheduleLogShiftDto }) shift: ScheduleLogShiftDto;
  @ApiProperty() amount: number;
  @ApiProperty() hours_worked: number;
}

export class ScheduleLogListItemDto {
  @ApiProperty({ description: 'Schedule log ID' })
  id: number;

  @ApiProperty({ description: 'Schedule ID', nullable: true })
  schedule_id: number | null;

  @ApiProperty({ description: 'Company ID', nullable: true })
  company_id: number | null;

  @ApiProperty({ description: 'Employee ID', nullable: true })
  employee_id: string | null;

  @ApiProperty({ description: 'Status', nullable: true })
  status: string | null;

  @ApiProperty({ description: 'Clock in time', type: String, format: 'date-time', nullable: true })
  clock_in_at: string | null;

  @ApiProperty({ description: 'Clock out time', type: String, format: 'date-time', nullable: true })
  clock_out_at: string | null;

  @ApiProperty({ description: 'Note', nullable: true })
  note: string | null;

  @ApiProperty({ description: 'Admin note', nullable: true })
  admin_note?: string | null;

  @ApiProperty({ description: 'Duration in minutes', nullable: true })
  duration: number | null;

  @ApiProperty({ description: 'Created at', type: String, format: 'date-time' })
  created_at: string;

  @ApiProperty({ description: 'Approved at', type: String, format: 'date-time', nullable: true })
  approved_at?: string | null;

  @ApiProperty({ description: 'Work duration in minutes' })
  work_duration: number;

  @ApiProperty({ description: 'Overtime duration in minutes' })
  overtime_duration: number;

  @ApiProperty({ description: 'Total pay', type: String })
  total_pay: string;

  @ApiProperty({ description: 'Overtime pay', type: String })
  overtime_pay: string;

  @ApiProperty({ description: 'Work pay', type: String })
  work_pay: string;

  @ApiProperty({ description: 'Is time sheet updated?' })
  time_sheet_updated: boolean;

  @ApiProperty({ description: 'Time sheet ID', nullable: true })
  time_sheet_id?: number | null;

  @ApiProperty({ description: 'Break time in minutes' })
  break_time: number;

  @ApiProperty({ description: 'Employee details', type: ScheduleLogEmployeeDto })
  employee: ScheduleLogEmployeeDto;

  @ApiProperty({ description: 'Work pay split details', type: [ScheduleLogPaySplitDto] })
  work_pay_split: ScheduleLogPaySplitDto[];

  @ApiProperty({ description: 'Overtime pay split details', type: [ScheduleLogPaySplitDto] })
  overtime_pay_split: ScheduleLogPaySplitDto[];
}

export class ScheduleLogListAdminResponseDto {
  @ApiProperty({
    example: {
      page: 1,
      limit: 10,
      total_data: 2,
      total_pages: 1,
    },
  })
  paginate: {
    page: number;
    limit: number;
    total_data: number;
    total_pages: number;
  };

  @ApiProperty({ type: [ScheduleLogListItemDto] })
  data: ScheduleLogListItemDto[];
}