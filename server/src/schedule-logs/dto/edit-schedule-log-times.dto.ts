import { IsString, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class EditScheduleLogTimesDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  id: number;

  @ApiProperty({ example: '2025-05-21 11:00 AM' })
  @IsOptional()
  @IsString()
  clock_in_at?: string;

  @ApiProperty({ example: '2025-05-21 07:00 PM' })
  @IsOptional()
  @IsString()
  clock_out_at?: string;

  @ApiProperty({ example: '2025-05-21 02:00 PM' })
  @IsOptional()
  @IsString()
  break_start?: string;

  @ApiProperty({ example: '2025-05-21 03:00 PM' })
  @IsOptional()
  @IsString()
  break_end?: string;
}