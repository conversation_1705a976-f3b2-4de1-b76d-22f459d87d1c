import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString } from 'class-validator';

export class ClockInDto {
    @ApiProperty({ example: "32e8b688-d5cd-4861-90ea-5630e0f4a57f" })
    @IsString()
    employee_id: string;

    @ApiProperty({ example: 1 })
    @IsInt()
    schedule_id: number;

    @ApiProperty({ example: "note" })
    @IsString()
    @IsOptional()
    note: string;

    @ApiProperty({ example: '2025-05-21 11:00 AM' })
    @IsOptional()
    @IsString()
    clock_in_at?: string;
}