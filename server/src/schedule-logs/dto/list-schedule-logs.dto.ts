import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray, IsEnum, IsDateString, IsUUID } from 'class-validator';
import { PaginateDto } from 'src/common/dto/paginate.dto';



export class ListScheduleLogsDto extends PaginateDto {
    @ApiPropertyOptional()
    @IsOptional()
    company_id?: number;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    statuses?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    employee_id?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsDateString()
    start_date?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsDateString()
    end_date?: string;

    @ApiPropertyOptional()
    @IsOptional()
    @IsUUID()
    created_by?: string;

    @IsOptional()
    limit: number;

    @IsOptional()
    page: number;
}