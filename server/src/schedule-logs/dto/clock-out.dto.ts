import { ApiProperty } from '@nestjs/swagger';
import { IsInt, <PERSON>N<PERSON>ber, IsOptional, IsString, ValidateIf, IsNotEmpty } from 'class-validator';

export class ClockOutDto {
  @ApiProperty({ example: 1 })
  @IsInt()
  schedule_log_id: number;

  @ApiProperty({ example: "32e8b688-d5cd-4861-90ea-5630e0f4a57f" })
  @IsString()
  employee_id: string;

  @ApiProperty({ example: "note" })
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty({ example: "2025-05-21 11:00 PM" })
  @IsString()
  @IsOptional()
  clock_out_at: string;

  @ApiProperty({ example: "2025-05-21 11:00 PM" })
  @IsString()
  @IsOptional()
  break_start: string;

  @ApiProperty({ example: "2025-05-21T17:00:00.000Z" })
  @IsString()
  @ValidateIf((o) => o.break_start !== undefined && o.break_start !== null)
  @IsNotEmpty({ message: 'break_end is required when break_start is provided' })
  break_end: string;

  @ApiProperty({ example: 60 })
  @IsNumber()
  @IsOptional()
  break_time: number;
}