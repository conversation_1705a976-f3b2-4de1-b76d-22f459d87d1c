import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { CreateScheduleLogDto } from './dto/create-schedule-log.dto';
import { UpdateScheduleLogDto } from './dto/update-schedule-log.dto';
import { ClockInDto } from './dto/clock-in.dto';
import { ClockOutDto } from './dto/clock-out.dto';
import { EditScheduleLogTimesDto } from './dto/edit-schedule-log-times.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { ScheduleLog } from './entities/schedule-log.entity';
import { ListScheduleLogsDto } from './dto/list-schedule-logs.dto';
import { PaginateResponseDto } from 'src/common/dto/paginate.dto';
import { schedule_log_status, schedule_status } from '@prisma/client';
import { ScheduleLogAdminActionDto } from './dto/schedule-log-admin-action.dto';
import { ScheduleLogResponseDto } from './dto/schedule-log-response.dto';
import { ScheduleLogsUtilsService } from './services/schedule-logs-utils.service';
import { AuthUser } from 'src/common/interface/auth_user.interface';
import { toZonedTime, formatInTimeZone } from 'date-fns-tz';
import { format, formatDuration, parse } from 'date-fns';


function parseAndFormatToUtc(input: string): Date {
  const parsed = parse(input, 'yyyy-MM-dd hh:mm a', new Date());
  // Create a new Date object from the formatted string to store it as a proper Date
  const formatted = format(parsed, 'yyyy-MM-dd HH:mm:ss') + '+00';
  return new Date(formatted);
}

function getCurrentTimeAsLocalString(): string {
  // Get current time
  const now = new Date();

  // Get timezone from environment variable, fallback to system timezone
  const timezone = process.env.TIMEZONE || Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Format to specified timezone in the desired format
  return formatInTimeZone(now, timezone, 'yyyy-MM-dd hh:mm a');
}
@Injectable()
export class ScheduleLogsService {
  // Replace with actual DB logic
  constructor(
    private prisma: PrismaService,
    private scheduleLogsUtilsService: ScheduleLogsUtilsService,
  ) { }

  async hasOpenSchedule(employee_id: string): Promise<boolean> {
    const scheduleLogNotClockedOut = await this.prisma.schedule_logs.findFirst({
      where: {
        employee_id,
        clock_out_at: null,
      },
    });
    if (scheduleLogNotClockedOut) {
      return true;
    }
    return false;
  }
  async scheduleExists(employee_id: string, schedule_id: number) {
    const scheduleLog = await this.prisma.schedule_logs.findFirst({
      where: {
        employee_id,
        schedule_id,
      },
    });
    if (scheduleLog) {
      return true;
    }
    return false;
  }
  async clockIn(dto: ClockInDto): Promise<ScheduleLog> {
    // return getCurrentTimeAsLocalString()
    const employee = await this.prisma.employee.findUnique({
      where: {
        id: dto.employee_id,
      },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found.');
    }
    const schedule = await this.prisma.schedules.findFirst({
      where: {
        employee_id: (dto.employee_id),
        id: dto.schedule_id
      },
    });
    
    if (!schedule) {
      throw new NotFoundException('Schedule not found.');
    }

    let clock_in_at;
    if (dto.clock_in_at) {
      // Convert provided clock-in time to UTC
      clock_in_at = parseAndFormatToUtc(dto.clock_in_at);
    } else {
      // Use current time in user's timezone, converted to UTC
      clock_in_at = parseAndFormatToUtc(getCurrentTimeAsLocalString());
    }
    const log = await this.prisma.schedule_logs.create({
      data: {
        employee_id: dto.employee_id,
        clock_in_at,
        schedule_id: dto.schedule_id,
        company_id: employee.company_id,
        note: dto.note ?? null,
        status: schedule_log_status.started,
      },
    });
    await this.prisma.schedules.update({
      data: { status: schedule_status.ongoing },
      where: { id: dto.schedule_id },
    });
    return log;
  }

  async clockOut(dto: ClockOutDto): Promise<any> {
    const log = await this.prisma.schedule_logs.findFirst({
      where: {
        id: dto.schedule_log_id,
        employee_id: dto.employee_id,
      },
      include: {
        schedules: {
          select: {
            id: true,
            end_time: true,
            end_date: true,
            duration: true,
          },
        },
      },
    });

    if (!log) {
      throw new NotFoundException('Schedule log not found.');
    }

    if (!log.clock_in_at) {
      throw new BadRequestException('Schedule not clocked in.');
    }
    // if (log.clock_out_at) throw new BadRequestException('ScheduleLog already clocked out.');

    const clockInAt = new Date(log.clock_in_at);
    let clockOutAt;
    if (dto.clock_out_at) {
      // Convert provided clock-in time to UTC
      clockOutAt = parseAndFormatToUtc(dto.clock_out_at);
    } else {
      // Use current time in user's timezone, converted to UTC
      clockOutAt = parseAndFormatToUtc(getCurrentTimeAsLocalString());
    }
    // console.log(clockOutAt)
    const scheduleEnd = log.schedules?.end_date
      ? toZonedTime(new Date(log.schedules.end_date), 'UTC')
      : null;

    const isOvertime = scheduleEnd ? clockOutAt > scheduleEnd : false;
    const duration = Math.floor((clockOutAt.getTime() - clockInAt.getTime()) / 60000);

    let break_time = 0;
    let break_start_utc: Date | undefined;
    let break_end_utc: Date | undefined;

    if (dto.break_start && dto.break_end) {
      // Parse break_start and break_end from "2025-05-21 11:00 PM" to Date (local)
      break_start_utc = parseAndFormatToUtc(dto.break_start);
      break_end_utc = parseAndFormatToUtc(dto.break_end);

      // Convert to UTC ISO string if you want to store as UTC string, or keep as Date
      // break_start_utc = new Date(break_start_utc.toISOString());
      // break_end_utc = new Date(break_end_utc.toISOString());

      // Validation: break_start must be after clockInAt
      // if (break_start_utc <= clockInAt) {
      //   throw new BadRequestException('Break start must be after clock in time.');
      // }

      // Validation: break_end must be after break_start
      // if (break_end_utc <= break_start_utc) {
      //   throw new BadRequestException('Break end must be after break start.');
      // }

      // Validation: break_end must be before or equal to scheduleEnd (if overtime) or clockOutAt
      // if (isOvertime && scheduleEnd) {
      //   if (break_end_utc > scheduleEnd) {
      //     throw new BadRequestException('Break end cannot be after scheduled end time.');
      //   }
      // } else {
      //   if (break_end_utc > clockOutAt) {
      //     throw new BadRequestException('Break end cannot be after clock out time.');
      //   }
      // }

      // Calculate break duration in minutes
      break_time = Math.floor((break_end_utc.getTime() - break_start_utc.getTime()) / 60000);

      // Optionally, check break_time is not negative or too large
      const maxAllowableBreak = isOvertime && scheduleEnd
        ? Math.floor((scheduleEnd.getTime() - clockInAt.getTime()) / 60000)
        : duration;
      if (break_time > duration) {
        throw new BadRequestException('Break time cannot exceed work duration.');
      }

      // if (maxAllowableBreak < 240) {
      //   throw new BadRequestException('Break time cannot be applied for work durations less than 4 hours.');
      // }
    }

    await this.prisma.schedule_logs.update({
      where: { id: log.id },
      data: {
        clock_out_at: clockOutAt,
        duration,
        note: dto.note ?? log.note,
        break_time,
        ...(break_start_utc && { break_start: break_start_utc }),
        ...(break_end_utc && { break_end: break_end_utc }),
      },
    });

    const updatedLog = await this.scheduleLogsUtilsService.calculateDurationAndAmounts(log.id);
    if (log.schedule_id) {
      await this.prisma.schedules.update({
        where: { id: log.schedule_id },
        data: {
          is_finished: true,
          status: schedule_status.completed,
        },
      });
    }

    return updatedLog;
  }


  async listLogs(dto: ListScheduleLogsDto): Promise<PaginateResponseDto> {
    const where: any = {};

    if (dto.company_id) where.company_id = dto.company_id;
    if (dto.employee_id) where.employee_id = dto.employee_id;

    if (dto.statuses && dto.statuses.length) {
      const statuses = Array.isArray(dto.statuses)
        ? dto.statuses
        : dto.statuses.split(',');
      where.status = { in: statuses };
    }

    if (dto.start_date || dto.end_date) {
      where.clock_in_at = {};
      if (dto.start_date) {
        const start = new Date(dto.start_date);
        start.setHours(0, 0, 0, 0);
        where.clock_in_at.gte = start;
      }
      if (dto.end_date) {
        const end = new Date(dto.end_date);
        end.setHours(23, 59, 59, 999);
        where.clock_in_at.lte = end;
      }
    }
    if (dto.created_by) {
      where.schedules = { created_by: dto.created_by };
    }
    // console.log(where)
    const page = dto.page && dto.page > 0 ? dto.page : 1;
    const limit = dto.limit && dto.limit > 0 ? dto.limit : 10;
    const skip = (page - 1) * limit;

    const [data, total_data] = await Promise.all([
      this.prisma.schedule_logs.findMany({
        where,
        skip,
        take: limit,
        orderBy: { clock_in_at: 'desc' },
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      }),
      this.prisma.schedule_logs.count({ where }),
    ]);

    const total_pages = Math.ceil(total_data / limit);

    return {
      paginate: {
        page,
        limit,
        total_data,
        total_pages,
      },
      data,
    };
  }

  async listLogsEmployee(
    dto: ListScheduleLogsDto,
  ): Promise<PaginateResponseDto> {
    const where: any = {};

    if (dto.company_id) where.company_id = dto.company_id;
    if (dto.employee_id) where.employee_id = dto.employee_id;

    if (dto.statuses && dto.statuses.length) {
      const statuses = Array.isArray(dto.statuses)
        ? dto.statuses
        : dto.statuses.split(',');
      where.status = { in: statuses };
    }

    if (dto.start_date || dto.end_date) {
      where.clock_in_at = {};
      if (dto.start_date) {
        const start = new Date(dto.start_date);
        start.setHours(0, 0, 0, 0);
        where.clock_in_at.gte = start;
      }
      if (dto.end_date) {
        const end = new Date(dto.end_date);
        end.setHours(23, 59, 59, 999);
        where.clock_in_at.lte = end;
      }
    }

    const page = dto.page && dto.page > 0 ? dto.page : 1;
    const limit = dto.limit && dto.limit > 0 ? dto.limit : 10;
    const skip = (page - 1) * limit;

    const [data, total_data] = await Promise.all([
      this.prisma.schedule_logs.findMany({
        where,
        skip,
        take: limit,
        orderBy: { clock_in_at: 'desc' },
      }),
      this.prisma.schedule_logs.count({ where }),
    ]);

    const total_pages = Math.ceil(total_data / limit);

    return {
      paginate: {
        page,
        limit,
        total_data,
        total_pages,
      },
      data,
    };
  }

  async viewLog(id: number) {
    const log = await this.prisma.schedule_logs.findUnique({
      where: { id: Number(id) },
      include: {
        schedules: {
          select: {
            id: true,
            start_date: true,
            end_date: true,
            start_time: true,
            end_time: true,
          },
        },
        employee: {
          select: {
            id: true,
            name: true,
            phone: true,
          },
        },
      },
    });
    if (!log) {
      throw new NotFoundException('Schedule log not found.');
    }
    return log;
  }

  async acceptLog(
    id: number,
    dto: ScheduleLogAdminActionDto,
  ): Promise<ScheduleLogResponseDto> {
    const log = await this.prisma.schedule_logs.findUnique({ where: { id } });
    if (!log) throw new NotFoundException('Schedule log not found.');
    if (log.status === schedule_log_status.approved)
      throw new BadRequestException('Already approved.');
    if (log.status != schedule_log_status.pending)
      throw new BadRequestException('Cannot approve.');

    const updated = await this.prisma.schedule_logs.update({
      where: { id },
      data: {
        status: schedule_log_status.approved,
        approved_at: new Date(),
        admin_note: dto.admin_note ?? null,
      },
    });

    // await this.scheduleLogsUtilsService.calculateDurationAndAmounts(id);

    return {
      id: updated.id,
      status: schedule_log_status.approved,
      approved_at: new Date(),
    };
  }

  async rejectLog(
    id: number,
    dto: ScheduleLogAdminActionDto,
  ): Promise<ScheduleLogResponseDto> {
    const log = await this.prisma.schedule_logs.findUnique({ where: { id } });
    if (!log) throw new NotFoundException('Schedule log not found.');
    if (log.status === schedule_log_status.rejected)
      throw new BadRequestException('Already rejected.');
    if (log.status != schedule_log_status.pending)
      throw new BadRequestException('Cannot reject.');

    const updated = await this.prisma.schedule_logs.update({
      where: { id },
      data: {
        status: schedule_log_status.rejected,
        admin_note: dto.admin_note ?? null,
      },
    });
    return {
      id: updated.id,
      status: schedule_log_status.rejected,
    };
  }

  async getStatusStats(
    query: {
      company_id?: number;
      employee_id?: string;
      start_date?: string;
      end_date?: string;
      created_by?: string
    },
    authUser: AuthUser,
  ) {
    const where: any = {};
    where.company_id = Number(authUser.company_id);
    if (query.employee_id) where.employee_id = query.employee_id;
    if (query.start_date || query.end_date) {
      where.clock_in_at = {};
      if (query.start_date) {
        const start = new Date(query.start_date);
        start.setHours(0, 0, 0, 0);
        where.clock_in_at.gte = start;
      }
      if (query.end_date) {
        const end = new Date(query.end_date);
        end.setHours(23, 59, 59, 999);
        where.clock_in_at.lte = end;
      }
    }
    if (query.created_by) {
      where.schedules = { created_by: query.created_by };
    }
    //
    // Single query to get all logs matching the filter
    const logs = await this.prisma.schedule_logs.findMany({
      where,
      select: { status: true, duration: true },
    });

    // Count by status
    const counts: Record<string, number> = {};
    let total_duration = 0;

    for (const log of logs) {
      const statusKey = log.status ?? 'unknown';
      counts[statusKey] = (counts[statusKey] || 0) + 1;
      total_duration += log.duration ?? 0;
    }

    return { counts, total_duration };
  }


  async editScheduleLogTimes(dto: EditScheduleLogTimesDto): Promise<ScheduleLog> {
    const log = await this.prisma.schedule_logs.findUnique({
      where: { id: dto.id },
      include: {
        schedules: {
          select: {
            id: true,
            end_date: true,
            duration: true,
          },
        },
      },
    });

    if (!log) {
      throw new NotFoundException('Schedule log not found.');
    }

    let updateData: any = {};
    let break_time = log.break_time;

    if (dto.clock_in_at) {
      updateData.clock_in_at = parseAndFormatToUtc(dto.clock_in_at);
    }

    if (dto.clock_out_at) {
      updateData.clock_out_at = parseAndFormatToUtc(dto.clock_out_at);
    }

    if (dto.break_start && dto.break_end) {
      const break_start = parseAndFormatToUtc(dto.break_start);
      const break_end = parseAndFormatToUtc(dto.break_end);

      // Validate break times
      if (break_end <= break_start) {
        throw new BadRequestException('Break end must be after break start.');
      }

      if (updateData.clock_in_at && break_start <= updateData.clock_in_at) {
        throw new BadRequestException('Break start must be after clock in time.');
      }

      if (updateData.clock_out_at && break_end >= updateData.clock_out_at) {
        throw new BadRequestException('Break end must be before clock out time.');
      }

      // Calculate break duration in minutes
      break_time = Math.floor((break_end.getTime() - break_start.getTime()) / 60000);
      updateData.break_start = break_start;
      updateData.break_end = break_end;
      updateData.break_time = break_time;
    } else {
      updateData.break_start = null;
      updateData.break_end = null;
      updateData.break_time = 0;
    }

    // Calculate duration if both clock in and out times are present
    if (updateData.clock_in_at || updateData.clock_out_at) {
      const clockInAt = updateData.clock_in_at || log.clock_in_at;
      const clockOutAt = updateData.clock_out_at || log.clock_out_at;

      if (clockInAt && clockOutAt) {
        updateData.duration = Math.floor((clockOutAt.getTime() - clockInAt.getTime()) / 60000);
      }
    }

    await this.prisma.schedule_logs.update({
      where: { id: log.id },
      data: updateData,
    });
    if (log.schedule_id) {
      await this.prisma.schedules.update({
        where: { id: log.schedule_id },
        data: {
          is_finished: true,
          status: schedule_status.completed,
        },
      });
    }
    return this.scheduleLogsUtilsService.calculateDurationAndAmounts(log.id);
  }

}
