import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { AuthError } from '@supabase/supabase-js';

@Catch(AuthError)
export class SupabaseExceptionFilter implements ExceptionFilter {
  catch(exception: AuthError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // Map Supabase error messages to appropriate HTTP status codes
    const errorMap = {
      'Invalid login credentials': HttpStatus.UNAUTHORIZED,
      'Email not confirmed': HttpStatus.UNAUTHORIZED,
      'User not found': HttpStatus.NOT_FOUND,
      'Email already registered': HttpStatus.CONFLICT,
    };

    const status = errorMap[exception.message] || HttpStatus.BAD_REQUEST;

    response.status(status).json({
      statusCode: status,
      message: exception.message,
      error: status === HttpStatus.UNAUTHORIZED ? 'Unauthorized' : 'Bad Request',
    });
  }
} 