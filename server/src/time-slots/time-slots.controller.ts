import { Controller, Get, UseGuards, Request, Put, Param, Body, ParseIntPipe, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TimeSlotsService } from './time-slots.service';
import { TimeSlotListResponseDto, UpdateTimeSlotDto, TimeSlotDto, CreateTimeSlotDto } from './dto/time-slot-list.dto';

@ApiTags('Time Slots')
@Controller('time-slots')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TimeSlotsController {
  constructor(private readonly timeSlotsService: TimeSlotsService) {}

  @Get()
  @ApiOperation({ summary: 'List time slots', description: 'Get all time slots for the company. Only accessible by company admins.' })
  @ApiResponse({ status: 200, description: 'List of time slots', type: TimeSlotListResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only company admins can access this endpoint' })
  async listTimeSlots(@Request() req): Promise<TimeSlotListResponseDto> {
    return this.timeSlotsService.listTimeSlots(req.user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a time slot', description: 'Update a time slot by ID. Only accessible by company admins.' })
  @ApiParam({ name: 'id', type: Number, description: 'Time slot ID' })
  @ApiResponse({ status: 200, description: 'The updated time slot', type: TimeSlotDto })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only company admins can access this endpoint' })
  async updateTimeSlot(
    @Request() req,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateTimeSlotDto
  ): Promise<TimeSlotDto> {
    return this.timeSlotsService.updateTimeSlot(req.user, id, updateDto);
  }

  @Post()
  @ApiOperation({ summary: 'Create a time slot', description: 'Create a new time slot. Only accessible by company admins.' })
  @ApiResponse({ status: 201, description: 'The created time slot', type: TimeSlotDto })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only company admins can access this endpoint' })
  async createTimeSlot(
    @Request() req,
    @Body() createDto: CreateTimeSlotDto
  ): Promise<TimeSlotDto> {
    return this.timeSlotsService.createTimeSlot(req.user, createDto);
  }
} 