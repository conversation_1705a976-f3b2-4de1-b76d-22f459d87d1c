import { Injectable, UnauthorizedException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { TimeSlotListResponseDto, UpdateTimeSlotDto, TimeSlotDto, CreateTimeSlotDto } from './dto/time-slot-list.dto';
import { format, parse } from 'date-fns';

function timeToMinutes(date: Date) {
  return date.getHours() * 60 + date.getMinutes();
}

@Injectable()
export class TimeSlotsService {
  constructor(private readonly prisma: PrismaService) { }

  async listTimeSlots(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }): Promise<TimeSlotListResponseDto> {
    if (user.role === 'company_admin') {
      if (!user.company_id) {
        throw new UnauthorizedException('Company admin must have a company_id');
      }

      const timeSlots = await this.prisma.time_slot_rates.findMany({
        where: {
          company_id: parseInt(user.company_id),
        },
        orderBy: {
          start_time: 'asc',
        },
      });

      const total = await this.prisma.time_slot_rates.count({
        where: {
          company_id: parseInt(user.company_id),
        },
      });

      return {
        time_slots: timeSlots.map(slot => ({
          ...slot,
          id: typeof slot.id === 'bigint' ? Number(slot.id) : slot.id,
          company_id: typeof slot.company_id === 'bigint' ? Number(slot.company_id) : slot.company_id,
          start_time: format(typeof slot.start_time === 'string' ? new Date(slot.start_time) : slot.start_time, 'hh:mm a'),
          end_time: format(typeof slot.end_time === 'string' ? new Date(slot.end_time) : slot.end_time, 'hh:mm a'),
          hourly_rate: slot.hourly_rate ? Number(slot.hourly_rate) : null,
          rate: slot.rate ? Number(slot.rate) : null,
          is_base_price: !!slot.is_base_price,
        })),
        total,
      };
    } else {
      throw new UnauthorizedException('Only company admins can view time slots');
    }
  }
  async updateTimeSlot(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }, id: number, updateDto: UpdateTimeSlotDto): Promise<TimeSlotDto> {
    if (user.role !== 'company_admin' || !user.company_id) {
      throw new UnauthorizedException('Only company admins can update time slots');
    }

    const slot = await this.prisma.time_slot_rates.findUnique({ where: { id } });
    if (!slot || Number(slot.company_id) !== Number(user.company_id)) {
      throw new NotFoundException('Time slot not found or not accessible');
    }

    // Prepare update data
    const updateData: any = {};
    if (updateDto.name !== undefined) updateData.name = updateDto.name;
    if (updateDto.start_time !== undefined) {
      const parsed = parse(updateDto.start_time, 'hh:mm a', new Date(0));
      if (isNaN(parsed.getTime())) throw new BadRequestException('Invalid start_time format');
      updateData.start_time = parsed;
    }
    if (updateDto.end_time !== undefined) {
      const parsed = parse(updateDto.end_time, 'hh:mm a', new Date(0));
      if (isNaN(parsed.getTime())) throw new BadRequestException('Invalid end_time format');
      updateData.end_time = parsed;
    }
    if (updateDto.break_time !== undefined) updateData.break_time = updateDto.break_time;
    if (updateDto.hourly_rate !== undefined) updateData.hourly_rate = updateDto.hourly_rate;
    if (updateDto.rate !== undefined) updateData.rate = updateDto.rate;
    if (updateDto.is_base_price !== undefined) updateData.is_base_price = !!updateDto.is_base_price;

    let hourly_rate = Number(slot.hourly_rate);

    if (updateDto.is_base_price) {
      hourly_rate = updateDto.rate ? Number(updateDto.rate) : Number(slot.rate);
      updateData.hourly_rate = hourly_rate;
      const lowerRate = await this.prisma.time_slot_rates.findFirst({
        where: {
          company_id: slot.company_id,
          id: { not: id },
          hourly_rate: { lt: hourly_rate }
        }
      });
      // if (lowerRate) {
      //   throw new BadRequestException('Cannot set as base price: another time slot has a lower hourly rate.');
      // }
      const remainingTimeSlots = await this.prisma.time_slot_rates.findMany({
        where: {
          company_id: slot.company_id,
          id: { not: id },
        }
      });
      for (const timeSlot of remainingTimeSlots) {
        await this.prisma.time_slot_rates.update({
          where: { id: timeSlot.id },
          data: {
            is_base_price: false,
            hourly_rate: Number(hourly_rate) + Number(timeSlot.rate),
          },
        });
      }

    }
    if ((updateDto.hourly_rate == undefined || null) && updateDto.rate) {
      const basePrice = await this.prisma.time_slot_rates.findFirst({
        where: {
          company_id: slot.company_id, // or createDto.company_id if in create
          is_base_price: true,
        },
      });
      if (basePrice && !updateDto.is_base_price) {
        updateData.hourly_rate = Number(basePrice.rate) + Number(updateDto.rate)
      } else {
        updateData.hourly_rate = Number(updateDto.rate)
      }
    }
    // is_base_price logic

    const updated = await this.prisma.time_slot_rates.update({
      where: { id },
      data: updateData,
    });

    return {
      ...updated,
      id: typeof updated.id === 'bigint' ? Number(updated.id) : updated.id,
      company_id: typeof updated.company_id === 'bigint' ? Number(updated.company_id) : updated.company_id,
      start_time: format(typeof updated.start_time === 'string' ? new Date(updated.start_time) : updated.start_time, 'hh:mm a'),
      end_time: format(typeof updated.end_time === 'string' ? new Date(updated.end_time) : updated.end_time, 'hh:mm a'),
      hourly_rate: updated.hourly_rate ? Number(updated.hourly_rate) : null,
      rate: updated.rate ? Number(updated.rate) : null,
      is_base_price: !!updated.is_base_price,
    };
  }

  async createTimeSlot(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }, createDto: CreateTimeSlotDto): Promise<TimeSlotDto> {
    if (user.role !== 'company_admin' || !user.company_id) {
      throw new UnauthorizedException('Only company admins can create time slots');
    }
    // Parse times
    const startTime = parse(createDto.start_time, 'hh:mm a', new Date(0));
    if (isNaN(startTime.getTime())) throw new BadRequestException('Invalid start_time format');
    const endTime = parse(createDto.end_time, 'hh:mm a', new Date(0));
    if (isNaN(endTime.getTime())) throw new BadRequestException('Invalid end_time format');

    // Overlap check
    const allSlots = await this.prisma.time_slot_rates.findMany({
      where: { company_id: createDto.company_id },
    });
    const newStart = timeToMinutes(startTime);
    const newEnd = timeToMinutes(endTime);
    for (const slot of allSlots) {
      const slotStart = timeToMinutes(typeof slot.start_time === 'string' ? new Date(slot.start_time) : slot.start_time);
      const slotEnd = timeToMinutes(typeof slot.end_time === 'string' ? new Date(slot.end_time) : slot.end_time);
      if (newStart < slotEnd && newEnd > slotStart) {
        throw new BadRequestException('Time slot overlaps with an existing slot.');
      }
    }
    let hourly_rate = createDto.hourly_rate ?? null;

    // is_base_price logic
    if (createDto.is_base_price) {
      const lowerRate = await this.prisma.time_slot_rates.findFirst({
        where: {
          company_id: createDto.company_id,
          hourly_rate: { lt: Number(createDto.hourly_rate) }
        }
      });
      if (lowerRate) {
        throw new BadRequestException('Cannot set as base price: another time slot has a lower hourly rate.');
      }
      await this.prisma.time_slot_rates.updateMany({
        where: { company_id: createDto.company_id },
        data: { is_base_price: false }
      });
    }
    if (createDto.hourly_rate == undefined && createDto.rate) {
      const basePrice = await this.prisma.time_slot_rates.findFirst({
        where: {
          company_id: createDto.company_id, // or createDto.company_id if in create
          is_base_price: true,
        },
      });
      if (basePrice && !createDto.is_base_price) {
        createDto.hourly_rate = Number(basePrice.rate) + Number(createDto.rate)
      } else {
        createDto.hourly_rate = Number(createDto.rate)
      }
    }

    const created = await this.prisma.time_slot_rates.create({
      data: {
        company_id: createDto.company_id,
        name: createDto.name,
        start_time: startTime,
        end_time: endTime,
        break_time: createDto.break_time,
        hourly_rate: hourly_rate,
        rate: createDto.rate ?? 0,
        is_base_price: !!createDto.is_base_price,
      },
    });

    return {
      ...created,
      id: typeof created.id === 'bigint' ? Number(created.id) : created.id,
      company_id: typeof created.company_id === 'bigint' ? Number(created.company_id) : created.company_id,
      start_time: format(typeof created.start_time === 'string' ? new Date(created.start_time) : created.start_time, 'hh:mm a'),
      end_time: format(typeof created.end_time === 'string' ? new Date(created.end_time) : created.end_time, 'hh:mm a'),
      hourly_rate: created.hourly_rate ? Number(created.hourly_rate) : null,
      rate: created.rate ? Number(created.rate) : null,
      is_base_price: !!created.is_base_price,
    };
  }


}