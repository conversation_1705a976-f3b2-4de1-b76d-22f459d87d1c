import { ApiProperty } from '@nestjs/swagger';
import { Prisma } from '@prisma/client';
import { IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsNotEmpty } from 'class-validator';

export class TimeSlotDto {
  @ApiProperty({ description: 'The unique identifier of the time slot', example: 1 })
  id: number;

  @ApiProperty({ description: 'The name of the time slot', nullable: true, example: 'Regular shift' })
  name: string | null;

  @ApiProperty({ description: 'The start time of the time slot (formatted)', example: '06:00 AM' })
  start_time: string;

  @ApiProperty({ description: 'The end time of the time slot (formatted)', example: '05:30 PM' })
  end_time: string;

  @ApiProperty({ description: 'The break time in minutes', example: 30 })
  break_time: number;

  @ApiProperty({ description: 'The hourly rate for this time slot', nullable: true, example: 20 })
  hourly_rate: number | null;

  @ApiProperty({ description: 'The rate for this time slot', nullable: true, example: 20 })
  rate: number | null;

  @ApiProperty({ description: 'The company ID this time slot belongs to', nullable: true, example: 1 })
  company_id: number | null;

  @ApiProperty({ description: 'When the time slot was created', example: '2025-05-19T08:29:52.810Z' })
  created_at: Date;

  @ApiProperty({ description: 'Is this the base price time slot?', example: false })
  is_base_price: boolean;
}

export class TimeSlotListResponseDto {
  @ApiProperty({ type: [TimeSlotDto], description: 'List of time slots' })
  time_slots: TimeSlotDto[];

  @ApiProperty({ description: 'Total number of time slots', example: 1 })
  total: number;
}

export class UpdateTimeSlotDto {
  @ApiProperty({ description: 'The name of the time slot', example: 'Regular shift', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({ description: 'The start time in hh:mm AM/PM format', example: '06:00 AM', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  start_time?: string;

  @ApiProperty({ description: 'The end time in hh:mm AM/PM format', example: '05:30 PM', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  end_time?: string;

  @ApiProperty({ description: 'The break time in minutes', example: 30, required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  break_time?: number;

  @ApiProperty({ description: 'The hourly rate for this time slot', example: 20, required: false })
  @IsOptional()
  @IsNumber()
  hourly_rate?: number;

  @ApiProperty({ description: 'The hourly rate for this time slot', example: 20 })
  @IsNumber()
  rate: number;

  @ApiProperty({ description: 'Is this the base price time slot?', example: false, required: false })
  @IsOptional()
  is_base_price?: boolean;
}

export class CreateTimeSlotDto {
  @ApiProperty({ description: 'The company ID this time slot belongs to', example: 1 })
  @IsNumber()
  company_id: number;

  @ApiProperty({ description: 'The name of the time slot', example: 'Regular shift', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'The start time in hh:mm AM/PM format', example: '06:00 AM' })
  @IsString()
  @IsNotEmpty()
  start_time: string;

  @ApiProperty({ description: 'The end time in hh:mm AM/PM format', example: '05:30 PM' })
  @IsString()
  @IsNotEmpty()
  end_time: string;

  @ApiProperty({ description: 'The break time in minutes', example: 30 })
  @IsNumber()
  @Min(0)
  break_time: number;

  @ApiProperty({ description: 'The hourly rate for this time slot', example: 20 })
  @IsOptional()
  @IsNumber()
  hourly_rate: number;

  @ApiProperty({ description: 'The hourly rate for this time slot', example: 20 })
  @IsNumber()
  rate: number;

  @ApiProperty({ description: 'Is this the base price time slot?', example: false, required: false })
  @IsOptional()
  is_base_price?: boolean;
}