import { Injectable, UnauthorizedException, ForbiddenException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EmployeeListResponseDto } from './dto/employee-list.dto';
import { AddEmployeeDto } from './dto/add-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { SupabaseService } from '../supabase/supabase.service';
import { EngagespotService } from 'src/engagespot/engagespot.service';

@Injectable()
export class EmployeesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly supabase: SupabaseService,
    private readonly engagespotService: EngagespotService,
  ) { }

  async listEmployees(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }): Promise<EmployeeListResponseDto> {
    console.log('Listing employees for user:', user);

    if (user.role === 'company_admin' || user.role === 'company_staff') {
      if (!user.company_id) {
        throw new UnauthorizedException('Company admin must have a company_id');
      }

      const employees = await this.prisma.employee.findMany({
        where: {
          company_id: parseInt(user.company_id),
        },
        select: {
          id: true,
          first_name: true,
          last_name: true,
          name: true,
          phone: true,
          company_id: true,
          created_at: true,
          employee_number: true,
          has_driving_license: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          user_roles: {
            select: {
              users: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: 'desc'
        }
      });

      const transformedEmployees = employees.map(emp => ({
        id: emp.id,
        first_name: emp.first_name,
        last_name: emp.last_name,
        name: emp.name,
        phone: emp.phone,
        company_id: emp.company_id,
        created_at: emp.created_at,
        email: emp.user_roles?.[0]?.users?.email || null,
        company: emp.company,
        employee_number: emp.employee_number,
        has_driving_license: emp.has_driving_license || undefined
      }));

      const total = await this.prisma.employee.count({
        where: {
          company_id: parseInt(user.company_id),
        },
      });
      return { employees: transformedEmployees, total };
    } else if (user.role === 'super_admin') {
      const employees = await this.prisma.employee.findMany({
        select: {
          id: true,
          first_name: true,
          last_name: true,
          name: true,
          phone: true,
          company_id: true,
          created_at: true,
          employee_number: true,
          has_driving_license: true,
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          user_roles: {
            select: {
              users: {
                select: {
                  email: true,
                },
              },
            },
          },
        },
      });

      const transformedEmployees = employees.map(emp => ({
        id: emp.id,
        first_name: emp.first_name,
        last_name: emp.last_name,
        name: emp.name,
        phone: emp.phone,
        company_id: emp.company_id,
        created_at: emp.created_at,
        email: emp.user_roles?.[0]?.users?.email || null,
        company: emp.company,
        employee_number: emp.employee_number,
        has_driving_license: emp.has_driving_license || undefined
      }));

      const total = await this.prisma.employee.count();
      return { employees: transformedEmployees, total };
    } else {
      throw new UnauthorizedException('Unauthorized to view employees');
    }
  }

  private async notifyEmployeeCreated(employee: any, addEmployeeDto: AddEmployeeDto) {
    await this.engagespotService.createOrUpdateUser(employee.id, {
      mobile: addEmployeeDto.phone_number,
      name: employee.name,
      email: addEmployeeDto.email,
    });
    await this.engagespotService.send('employee_created', [employee.id], {
      employee_name: employee.name,
      email: employee.email_address,
      password: addEmployeeDto.password,
    });
  }

  async addEmployee(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }, addEmployeeDto: AddEmployeeDto) {
    // Authorization checks
    if (user.role === 'company_admin' || user.role === 'company_staff') {
      const userCompanyId = parseInt(user.company_id || '0');
      if (userCompanyId !== addEmployeeDto.company_id) {
        throw new ForbiddenException('You can only add employees to your own company');
      }
    } else if (user.role !== 'super_admin') {
      throw new ForbiddenException('Only super admins and company admins can add employees');
    }

    // Check if employee_number exists for this company
    if (addEmployeeDto.employee_number) {
      const exists = await this.prisma.employee.findFirst({
        where: {
          company_id: addEmployeeDto.company_id,
          employee_number: addEmployeeDto.employee_number,
        },
      });
      if (exists) {
        throw new BadRequestException('Employee number already exists for this company');
      }
    }

    // Create user in Supabase auth using admin client
    const { data: authUser, error: authError } = await this.supabase.adminClient.auth.admin.createUser({
      email: addEmployeeDto.email,
      password: addEmployeeDto.password,
      email_confirm: true,
      user_metadata: {
        display_name: addEmployeeDto.name ? addEmployeeDto.name : `${addEmployeeDto.first_name} ${addEmployeeDto.last_name}`,
      }
    });

    if (authError) {
      throw new Error(`Failed to create user: ${authError.message}`);
    }

    if (!authUser.user) {
      throw new Error('Failed to create user: No user data returned');
    }

    // Create employee record with the Supabase user ID
    const employee = await this.prisma.employee.create({
      data: {
        id: authUser.user.id,
        first_name: addEmployeeDto.first_name,
        last_name: addEmployeeDto.last_name,
        name: addEmployeeDto.name ? addEmployeeDto.name : `${addEmployeeDto.first_name} ${addEmployeeDto.last_name}`,
        phone: addEmployeeDto.phone_number,
        company_id: addEmployeeDto.company_id,
        email_address: addEmployeeDto.email,
        employee_number: addEmployeeDto.employee_number,
        has_driving_license: addEmployeeDto.has_driving_license || false

      },
      include: {
        company: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create user role mapping
    await this.prisma.user_roles.create({
      data: {
        user_id: authUser.user.id,
        role: 'employee',
        employee_id: employee.id,
        company_id: addEmployeeDto.company_id,
      },
    });
    // Create or update user in Engagespot and send notification
    await this.notifyEmployeeCreated(employee, addEmployeeDto);

    // Return employee with email
    return {
      ...employee,
      email: addEmployeeDto.email,
    };
  }

  async editEmployee(employeeId: string, dto: UpdateEmployeeDto, user: any) {
    // if (user.role != 'company_admin' || user.role != 'company_staff') {
    //   throw new ForbiddenException('Only company admins can edit employees');
    // }
    const employee = await this.prisma.employee.findUnique({ where: { id: employeeId } });
    if (!employee) throw new NotFoundException('Employee not found');
    if (String(employee.company_id) !== String(user.company_id)) {
      throw new ForbiddenException('You can only edit employees in your own company');
    }

    // Check if employee_number is being changed and already exists for this company
    if (
      dto.employee_number &&
      dto.employee_number !== employee.employee_number
    ) {
      const exists = await this.prisma.employee.findFirst({
        where: {
          company_id: employee.company_id,
          employee_number: dto.employee_number,
          NOT: { id: employeeId },
        },
      });
      if (exists) {
        throw new BadRequestException('Employee number already exists for this company');
      }
    }

    const updateData: any = {};
    if (dto.first_name !== undefined) updateData.first_name = dto.first_name;
    if (dto.last_name !== undefined) updateData.last_name = dto.last_name;
    if (dto.first_name !== undefined || dto.last_name !== undefined) {
      updateData.name = `${dto.first_name ?? employee.first_name ?? ''} ${dto.last_name ?? employee.last_name ?? ''}`.trim();
    }
    if (dto.has_driving_license !== undefined) updateData.has_driving_license = dto.has_driving_license;

    if (dto.phone_number !== undefined) updateData.phone = dto.phone_number;
    if (dto.employee_number !== undefined) updateData.employee_number = dto.employee_number;
    // Email and password are managed in Supabase auth
    if (dto.email || dto.password) {
      if (dto.email) updateData.email_address = dto.email;
      const updates: any = {};
      if (dto.email) updates.email = dto.email;
      if (dto.password) updates.password = dto.password;
      const { error } = await this.supabase.adminClient.auth.admin.updateUserById(employeeId, updates);
      if (error) throw new BadRequestException('Failed to update user in Supabase: ' + error.message);
    }
    if (Object.keys(updateData).length > 0) {
      await this.prisma.employee.update({
        where: { id: employeeId },
        data: updateData,
      });
    }
    const engagespotUser: { mobile?: string; name?: string; email?: string } = {
      mobile: dto.phone_number ?? employee.phone ?? undefined,
      name: employee.name ?? undefined
    };
    if (dto.email) {
      engagespotUser.email = dto.email;
    }

    await this.engagespotService.createOrUpdateUser(employeeId, engagespotUser);

    return { message: 'Employee updated successfully' };
  }

  async deleteEmployee(employeeId: string, user: any) {
    // if (user.role !== 'company_admin') {
    //   throw new ForbiddenException('Only company admins can delete employees');
    // }

    const employee = await this.prisma.employee.findUnique({ where: { id: employeeId } });
    if (!employee) throw new NotFoundException('Employee not found');
    if (String(employee.company_id) !== String(user.company_id)) {
      throw new ForbiddenException('You can only delete employees in your own company');
    }

    // Check if schedules exist for this employee
    const scheduleCount = await this.prisma.schedules.count({
      where: { employee_id: employeeId },
    });
    if (scheduleCount > 0) {
      throw new BadRequestException('Cannot delete employee with existing schedules');
    }

    // Delete user roles mapping
    await this.prisma.user_roles.deleteMany({ where: { employee_id: employeeId } });

    // Delete employee record
    await this.prisma.employee.delete({ where: { id: employeeId } });

    // Optionally, delete user from Supabase auth (if desired)
    await this.supabase.adminClient.auth.admin.deleteUser(employeeId);

    return { message: 'Employee deleted successfully' };
  }
}