import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Matches, MinLength, IsN<PERSON>ber, IsO<PERSON>al, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export class AddEmployeeDto {
  @ApiProperty({
    description: 'Employee first name',
    example: '<PERSON>'
  })
  @IsNotEmpty({ message: 'First name is required' })
  @IsString({ message: 'First name must be a string' })
  first_name: string;

  @ApiProperty({
    description: 'Employee last name',
    example: 'Do<PERSON>'
  })
  @IsNotEmpty({ message: 'Last name is required' })
  @IsString({ message: 'Last name must be a string' })
  last_name: string;

  @IsOptional({ message: 'name is required' })
  @IsString({ message: 'name must be a string' })
  name: string;
  @ApiProperty({
    description: 'Employee email',
    example: '<EMAIL>'
  })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @ApiProperty({
    description: 'Employee phone number (numeric only)',
    example: '1234567890'
  })
  @IsNotEmpty({ message: 'Phone number is required' })
  @Matches(/^\d+$/, { message: 'Phone number must contain only digits' })
  phone_number: string;

  @ApiProperty({
    description: 'Employee password (minimum 6 characters)',
    example: 'password123'
  })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;

  @ApiProperty({
    description: 'Company ID',
    example: 1
  })
  @IsNotEmpty({ message: 'Company ID is required' })
  @IsNumber({}, { message: 'Company ID must be a number' })
  @Type(() => Number)
  company_id: number;

  @ApiProperty({
    description: 'Employee number (optional, unique identifier for employee within company)',
    example: 'EMP-001',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Employee number must be a string' })
  employee_number?: string;

    
  @ApiProperty({ description: 'Employee has_driving_license', example:true, required: false })
  @IsBoolean()
  @IsOptional()
  has_driving_license: boolean;
}