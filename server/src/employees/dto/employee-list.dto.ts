import { ApiProperty } from '@nestjs/swagger';

export class EmployeeDto {
  @ApiProperty({ description: 'Employee ID' })
  id: string;

  @ApiProperty({ description: 'Employee first name' })
  first_name: string | null;

  @ApiProperty({ description: 'Employee last name' })
  last_name: string | null;

  @ApiProperty({ description: 'Employee full name' })
  name: string | null;

  @ApiProperty({ description: 'Employee phone number' })
  phone: string | null;

  @ApiProperty({ description: 'Company ID' })
  company_id: number;

  @ApiProperty({ description: 'Created at timestamp' })
  created_at: Date;

  @ApiProperty({ description: 'Employee number (unique within company)', example: 'EMP-001', required: false })
  employee_number?: string | null;
  
  @ApiProperty({ description: 'Employee has_driving_license', example:true, required: false })
  has_driving_license?: boolean;
}

export class EmployeeListResponseDto {
  @ApiProperty({ description: 'List of employees', type: [EmployeeDto] })
  employees: EmployeeDto[];

  @ApiProperty({ description: 'Total count of employees' })
  total: number;
}