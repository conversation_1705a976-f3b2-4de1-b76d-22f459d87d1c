import { Controller, Get, UseGuards, Req, Post, Body, Put, Param, Request, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { EmployeesService } from './employees.service';
import { EmployeeListResponseDto } from './dto/employee-list.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RequestWithUser } from '../auth/jwt-auth.guard';
import { AddEmployeeDto } from './dto/add-employee.dto';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UpdateEmployeeDto } from './dto/update-employee.dto';

@ApiTags('employees')
@ApiBearerAuth()
@Controller('employees')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EmployeesController {
  constructor(private readonly employeesService: EmployeesService) { }

  @Get()
  @ApiOperation({
    summary: 'List employees',
    description: 'Returns a list of employees. Super admins can see all employees, while company admins can only see employees from their company.'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of employees',
    type: EmployeeListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token'
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have permission to view employees'
  })
  async listEmployees(@Req() req: RequestWithUser): Promise<EmployeeListResponseDto> {
    console.log('Employees endpoint hit. User:', req.user);
    return this.employeesService.listEmployees(req.user);
  }

  @Post()
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({
    summary: 'Add new employee',
    description: 'Add a new employee to the company. Super admins can add employees to any company, while company admins can only add employees to their own company.'
  })
  @ApiResponse({
    status: 201,
    description: 'Employee added successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or attempting to add employee to different company' })
  async addEmployee(@Req() req: RequestWithUser, @Body() addEmployeeDto: AddEmployeeDto) {
    return this.employeesService.addEmployee(req.user, addEmployeeDto);
  }

  @Put(':employeeId')
  @Roles('company_admin', 'company_staff')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: 'Edit an employee' })
  @ApiResponse({ status: 200, description: 'Employee updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  async editEmployee(
    @Param('employeeId') employeeId: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @Request() req,
  ) {
    return this.employeesService.editEmployee(employeeId, updateEmployeeDto, req.user);
  }

  @Delete(':employeeId')
  @Roles('company_admin','company_staff')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @ApiOperation({ summary: 'Edit an employee' })
  @ApiResponse({ status: 200, description: 'Employee deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  async deleteEmployee(
    @Param('employeeId') employeeId: string,
    @Request() req,
  ) {
    return this.employeesService.deleteEmployee(employeeId, req.user);
  }
} 