import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super({
      log: ['error', 'warn'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
    });
  }

  async onModuleInit() {
    try {
      await this.$connect();
      console.log('Successfully connected to database');
    } catch (error) {
      console.error('Failed to connect to database:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.$disconnect();
      console.log('Successfully disconnected from database');
    } catch (error) {
      console.error('Error disconnecting from database:', error);
    }
  }

  // Add retry logic for database operations
  async $executeRawWithRetry<T>(query: Prisma.Sql, params: any[] = [], maxRetries = 3): Promise<T> {
    let lastError: Error | null = null;
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await this.$executeRaw(query);
        return result as unknown as T;
      } catch (error) {
        lastError = error;
        if (error.code === '42P05') { // prepared statement already exists
          await new Promise(resolve => setTimeout(resolve, 100 * (i + 1))); // exponential backoff
          continue;
        }
        throw error;
      }
    }
    throw lastError || new Error('Max retries exceeded');
  }

  async $queryRawWithRetry<T>(query: Prisma.Sql, params: any[] = [], maxRetries = 3): Promise<T> {
    let lastError: Error | null = null;
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await this.$queryRaw(query);
        return result as unknown as T;
      } catch (error) {
        lastError = error;
        if (error.code === '42P05') { // prepared statement already exists
          await new Promise(resolve => setTimeout(resolve, 100 * (i + 1))); // exponential backoff
          continue;
        }
        throw error;
      }
    }
    throw lastError || new Error('Max retries exceeded');
  }
} 