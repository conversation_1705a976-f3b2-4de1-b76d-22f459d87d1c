import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { EngagespotService } from './engagespot.service';
import { CreateEngagespotDto } from './dto/create-engagespot.dto';
import { UpdateEngagespotDto } from './dto/update-engagespot.dto';

@Controller('engagespot')
export class EngagespotController {
  constructor(private readonly engagespotService: EngagespotService) {}


}
