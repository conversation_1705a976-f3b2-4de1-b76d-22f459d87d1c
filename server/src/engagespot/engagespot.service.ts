import { EngagespotClient } from '@engagespot/node';
import { Injectable } from '@nestjs/common';


@Injectable()
export class EngagespotService {
  private readonly apiKey: string;
  private readonly apiSecret: string;

  constructor() {
    this.apiKey = process.env.ENGAGESPOT_API_KEY || '2u1cifhcttiq4wigotmv89';
    this.apiSecret = process.env.ENGAGESPOT_API_SECRET || 'odkh74a0071mnshk0lp8hejfhbjgibgcj481eg35e5f5c7b5';
  }

  async send(
    type: string,
    recipients: Array<string>,
    data?: object,
    sendAt?: string,
  ) {
    try {
      const client = await this.engageSpotClient();
      client.send({
        notification: {
          workflow: { identifier: await this.getWorkflowIdentifier(type) },
        },
        data: data,
        sendTo: {
          recipients,
        },
        sendAt,
      });
      console.log(`engagespot notification sent: ${type}`);
      return true;
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  async createOrUpdateUser(identifier: string, user: { mobile?: string; name?: string; email?: string; }) {
    try {
      const client = await this.engageSpotClient();
      await client.createOrUpdateUser(identifier, {
        phoneNumber: user.mobile,
        name: user.name,
        email: user.email,
      });
      return true;
    } catch (err) {
      console.log(`engagespot user create error: ${err.message}`);
      return false;
    }
  }

  async engageSpotClient() {
    return EngagespotClient({ apiKey: this.apiKey, apiSecret: this.apiSecret });
  }

  async getWorkflowIdentifier(type: string) {
    let workflowIdentifier: any;

    switch (type) {
      case 'job_scheduled':
        workflowIdentifier = process.env.JOB_SCHEDULED || 'job_scheduled';
        break;
           case 'employee_created':
        workflowIdentifier = process.env.EMPLOYEE_CREATED || 'employee_created';
        break;

      default:
        workflowIdentifier = null;
    }
    return workflowIdentifier;
  }
}
