import PDFDocument from 'pdfkit';
import { Response } from 'express';
import { GroupedResponse, TimeSheetInterface } from './interface/timesheet-pdf.interface';

const PRIMARY_BLUE = '#4A90E2';
const LIGHT_GREEN = '#E8F5E8';
const LIGHT_RED = '#FFE8E8';
const LIGHT_BLUE = '#E3F0FC';
const BORDER_COLOR = '#E0E0E0';
const TEXT_DARK = '#333333';
const TEXT_LIGHT = '#666666';

function getMonthName(dateStr: string) {
   const date = new Date(dateStr);
   const monthNames = [
      '<PERSON><PERSON>ri', 'Februari', 'Mars', 'April', 'Maj', 'Juni',
      'Juli', 'Augusti', 'September', 'Oktober', 'November', 'December'
   ];
   return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
}

function numberToWords(num: number): string {
   // Simple Swedish number to words conversion for amounts
   const ones = ['', 'Ett', 'Två', 'Tre', 'Fyra', 'Fem', 'Sex', 'Sju', 'Åtta', 'Nio'];
   const teens = ['Tio', 'Elva', 'Tolv', 'Tretton', 'Fjorton', 'Femton', 'Sexton', 'Sjutton', 'Arton', 'Nitton'];
   const tens = ['', '', 'Tjugo', 'Trettio', 'Fyrtio', 'Femtio', 'Sextio', 'Sjuttio', 'Åttio', 'Nittio'];
   const thousands = ['', 'Tusen', 'Miljoner', 'Miljarder'];

   if (num === 0) return 'Noll Kronor';

   // Simplified conversion - for a complete implementation, you'd need more complex logic
   return `${num.toLocaleString('sv-SE')} Kronor`;
}

export function generateTimeSheetPdf(timeSheet: TimeSheetInterface, res: Response) {
   const doc = new PDFDocument({ margin: 30, size: 'A4' });
   res.setHeader('Content-Type', 'application/pdf');
   res.setHeader('Content-Disposition', `attachment; filename="lonespecifikation-${timeSheet.id}.pdf"`);

   doc.pipe(res);

   const pageWidth = doc.page.width - 60; // Account for margins
   let yPos = 40;

   // Header Section with Company Title (removed logo placeholder)
   doc.rect(30, yPos, pageWidth, 120).fill(PRIMARY_BLUE);

   // Reset and add company info (center aligned)
   doc.fillColor('white').fillOpacity(1);
   doc.fontSize(24).font('Helvetica-Bold')
      .text(timeSheet.company?.name || 'Företagsnamn', 30, yPos + 20, { align: 'center', width: pageWidth });

   doc.fontSize(12).font('Helvetica')
      .text(timeSheet.company?.registration_number ? `Företags-ID: ${timeSheet.company.registration_number}` : '', 30, yPos + 50, { align: 'center', width: pageWidth });

   // "LÖNESPECIFIKATION" title (center aligned)
   doc.fontSize(18).font('Helvetica-Bold')
      .text('LÖNESPECIFIKATION', 30, yPos + 80, { align: 'center', width: pageWidth });

   doc.fontSize(14).font('Helvetica')
      .text(getMonthName(timeSheet.period_from), 30, yPos + 105, { align: 'center', width: pageWidth });

   yPos += 140;

   // Employee and Pay Period Details Section
   doc.fillColor(TEXT_DARK).fillOpacity(1);

   // Left side - Employee Details
   doc.fontSize(14).font('Helvetica-Bold')
      .text('Anställningsuppgifter', 45, yPos);

   yPos += 25;
   doc.fontSize(11).font('Helvetica')
      .text(`Namn: ${timeSheet.employee?.name || 'N/A'}`, 45, yPos);

   yPos += 15;
   doc.text(`Anställnings-ID: ${timeSheet.employee?.employee_number || 'N/A'}`, 45, yPos);

   // Right side - Pay Period Details
   const rightColumnX = pageWidth / 2 + 30;
   yPos -= 40; // Reset to same level as left column

   doc.fontSize(14).font('Helvetica-Bold')
      .text('Löneperiod', rightColumnX, yPos);

   yPos += 25;
   doc.fontSize(11).font('Helvetica')
      .text(`Månad: ${getMonthName(timeSheet.period_from)}`, rightColumnX, yPos);

   yPos += 15;
   const startDate = new Date(timeSheet.period_from);
   const endDate = new Date(timeSheet.period_to);
   doc.text(`Löneperiod: ${startDate.toLocaleDateString('sv-SE')} - ${endDate.toLocaleDateString('sv-SE')}`, rightColumnX, yPos);

   yPos += 15;
   doc.text(`Utbetalas: ${timeSheet.paid_date ? new Date(timeSheet.paid_date).toLocaleDateString('sv-SE') : 'Ej fastställd'}`, rightColumnX, yPos);

   yPos += 40;

   // Earnings Section
   doc.fontSize(14).font('Helvetica-Bold').fillColor('#2E7D32')
      .text('Lön', 45, yPos);

   yPos += 20;

   // Earnings table header
   doc.rect(30, yPos, pageWidth, 25).fill(LIGHT_GREEN);
   doc.fillColor(TEXT_DARK).fillOpacity(1);

   doc.fontSize(10).font('Helvetica-Bold')
      .text('Skifttyp', 45, yPos + 8)
      .text('Timmar', 150, yPos + 8)
      .text('Timkostnad', 210, yPos + 8)
      .text('Grundlön', 280, yPos + 8)
      .text('Övertid', 350, yPos + 8)
      .text('Totalt', 420, yPos + 8);

   yPos += 25;

   // Render actual shift data from meta array
   const shiftsData = Array.isArray(timeSheet.meta) ? timeSheet.meta : [];
   doc.fontSize(9).font('Helvetica');

   shiftsData.forEach((shift: GroupedResponse, index: number) => {
      const bgColor = index % 2 === 0 ? '#FFFFFF' : '#F9F9F9';
      const totalAmount = shift.base_amount + shift.over_time;

      doc.rect(30, yPos, pageWidth, 22).fill(bgColor);
      doc.fillColor(TEXT_DARK).fillOpacity(1);

      doc.text(shift.name, 45, yPos + 6)
         .text(`${shift.total_work_hours}h`, 150, yPos + 6)
         .text(`${shift.rate.toLocaleString('sv-SE')} kr`, 210, yPos + 6)
         .text(`${shift.base_amount.toLocaleString('sv-SE')} kr`, 280, yPos + 6)
         .text(`${shift.over_time.toLocaleString('sv-SE')} kr`, 350, yPos + 6)
         .text(`${totalAmount.toLocaleString('sv-SE')} kr`, 420, yPos + 6);

      yPos += 22;
   });

   yPos += 10;

   // Extra Pay Section (only if extra_pay_total exists)
   if (timeSheet.extra_pay_total && Number(timeSheet.extra_pay_total) > 0) {
      doc.rect(30, yPos, pageWidth, 25).fill('#F0F8FF'); // Light blue background
      doc.fillColor('#1976D2').fillOpacity(1);
      doc.fontSize(11).font('Helvetica-Bold')
         .text('Extra ersättning', 45, yPos + 8)
         .text(`${Number(timeSheet.extra_pay_total).toLocaleString('sv-SE')} kr`, 45, yPos + 8, { align: 'right', width: pageWidth - 30 });

      yPos += 35;
   }

   // Total Earnings
   doc.rect(30, yPos, pageWidth, 30).fill(LIGHT_GREEN);
   doc.fillColor('#2E7D32').fillOpacity(1);
   doc.fontSize(12).font('Helvetica-Bold')
      .text('Bruttolön', 45, yPos + 10)
      .text(`${Number(timeSheet.total_pay || 0).toLocaleString('sv-SE')} kr`, 45, yPos + 10, { align: 'right', width: pageWidth - 30 });

   yPos += 50;

   // Deductions Section
   doc.fillColor('#C62828').fillOpacity(1);
   doc.fontSize(14).font('Helvetica-Bold')
      .text('Avdrag', 45, yPos);

   yPos += 25;

   // Tax deduction
   doc.rect(30, yPos, pageWidth, 25).fill('#FFFFFF');
   doc.strokeColor(BORDER_COLOR).rect(30, yPos, pageWidth, 25).stroke();
   doc.fillColor(TEXT_DARK).fillOpacity(1);

   const taxLabel = timeSheet.tax_meta?.percentage
      ? `Källskatt (${timeSheet.tax_meta.percentage}%)`
      : 'Källskatt';

   doc.fontSize(11).font('Helvetica')
      .text(taxLabel, 45, yPos + 8)
      .text(`${Number(timeSheet.deduction || 0).toLocaleString('sv-SE')} kr`, 45, yPos + 8, { align: 'right', width: pageWidth - 30 });

   yPos += 25;

   // Total Deductions
   doc.rect(30, yPos, pageWidth, 30).fill(LIGHT_RED);
   doc.fillColor('#C62828').fillOpacity(1);
   doc.fontSize(12).font('Helvetica-Bold')
      .text('Totala avdrag', 45, yPos + 10)
      .text(`${Number(timeSheet.deduction || 0).toLocaleString('sv-SE')} kr`, 45, yPos + 10, { align: 'right', width: pageWidth - 30 });

   yPos += 50;

   // Net Salary Section
   doc.rect(30, yPos, pageWidth, 60).fill(LIGHT_BLUE);
   doc.strokeColor(PRIMARY_BLUE).lineWidth(2).rect(30, yPos, pageWidth, 60).stroke();

   doc.fillColor(PRIMARY_BLUE).fillOpacity(1);
   doc.fontSize(16).font('Helvetica-Bold')
      .text('Nettolön', 45, yPos + 15);

   doc.fontSize(20).font('Helvetica-Bold')
      .text(`${Number(timeSheet.final_pay || 0).toLocaleString('sv-SE')} kr`, 45, yPos + 15, { align: 'right', width: pageWidth - 30 });

   doc.fontSize(10).font('Helvetica')
      .text(`Belopp i ord: ${numberToWords(Number(timeSheet.final_pay || 0))}`, 45, yPos + 40);

   yPos += 80;

   // Footer
   doc.fillColor(TEXT_LIGHT).fillOpacity(1);
   doc.fontSize(9).font('Helvetica')
      .text('Detta är en datorgenererad lönespecifikation och kräver ingen signatur.', 45, yPos + 20, { align: 'center', width: pageWidth });

   doc.text(`Genererad den: ${new Date().toLocaleDateString('sv-SE')}`, 45, yPos + 40, { align: 'center', width: pageWidth });

   doc.end();
}