export interface TimeSheetInterface {
  id: number;
  employee_id: string;
  company_id: number;
  period: string; // ISO date string
  period_from: string; // ISO date string
  period_to: string; // ISO date string

  total_duration_allotted: number;
  total_duration: number;
  total_duration_worked: number;
  total_duration_overtime: number;
  total_pay: string;
  total_pay_worked: string;
  total_pay_overtime: string;
  status: string;
  payment_type: string;
  paid_date: string; // ISO date string
  meta: GroupedResponse;
  note: string;
  created_at: string; // ISO date string
  tax_meta: {
    percentage: number;
    [key: string]: any;
  } | null;
  deduction: string;
  final_pay: string;
  extra_pay_total: string;

  employee: {
    id: string;
    name: string;
    phone: string;
    employee_number: string | null;
    email_address: string;
  };
  company: {
    name: string;
    registration_number: string | null;
  };
}

export interface TimeSlotRate {
  name: string;
  rate: string; // stored as string in input
  is_base_price: boolean;
}

export interface Entry {
  id: number;
  created_at: string;
  schedule_log_id: number;
  time_slot_rate_id: number;
  break_time: string;
  hours_worked: string;
  start: string;
  end: string;
  rate: string;
  amount: string;
  time_slot_rates: TimeSlotRate;
}

export interface GroupedResponse {
  time_slot_rate_id: number;
  name: string;
  total_work_hours: number;
  base_amount: number;
  over_time: number;
  rate: number;
}