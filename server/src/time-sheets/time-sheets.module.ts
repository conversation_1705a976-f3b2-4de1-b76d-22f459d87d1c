import { Module } from '@nestjs/common';
import { TimeSheetsService } from './time-sheets.service';
import { TimeSheetsController } from './time-sheets.controller';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PrismaService } from '../prisma/prisma.service';
import { TimeSheetsCronService } from './time-sheets-cron.service';
import { ExportModule } from 'src/export/export.module';

@Module({
  imports: [PrismaModule, SupabaseModule, ExportModule],
  controllers: [TimeSheetsController],
  providers: [TimeSheetsService, TimeSheetsCronService, PrismaService],
})
export class TimeSheetsModule { }
