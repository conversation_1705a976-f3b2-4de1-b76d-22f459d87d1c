export const timeSheetsListExample = {
  paginate: {
    page: 1,
    limit: 10,
    total_data: 2,
    total_pages: 1,
  },
  data: [
    {
      id: 1,
      employee_id: 'emp_123',
      company_id: 1,
      period: '2025-05-22',
      total_duration_allotted: 480,
      total_duration: 500,
      total_duration_worked: 480,
      total_duration_overtime: 20,
      total_pay: 1000,
      total_pay_worked: 800,
      total_pay_overtime: 200,
      status: 'approved',
      payment_type: 'bank',
      paid_date: '2025-05-23T00:00:00.000Z',
      meta: null,
      note: 'Monthly payout',
      created_at: '2025-05-22T12:00:00.000Z',
      employee: {
        id: 'emp_123',
        name: '<PERSON>',
        phone: '**********',
      },
    },
    {
      id: 2,
      employee_id: 'emp_456',
      company_id: 1,
      period: '2025-05-22',
      total_duration_allotted: 480,
      total_duration: 480,
      total_duration_worked: 480,
      total_duration_overtime: 0,
      total_pay: 900,
      total_pay_worked: 900,
      total_pay_overtime: 0,
      status: 'pending',
      payment_type: null,
      paid_date: null,
      meta: null,
      note: null,
      created_at: '2025-05-22T12:00:00.000Z',
      employee: {
        id: 'emp_456',
        name: '<PERSON>',
        phone: '**********',
      },
    },
  ],
};