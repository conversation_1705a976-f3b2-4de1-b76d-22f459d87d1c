import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { TimeSheetsListResponseDto, TimeSheetDto } from './dto/time-sheets-list.dto';
import { TimeSheetsListQueryDto } from './dto/time-sheets-list.query.dto';
import { PaginateResponseDto } from 'src/common/dto/paginate.dto';
import { PaymentType, UpdatePaymentDto } from './dto/update-time-sheet.dto';
import { time_sheet_status } from '@prisma/client';
import { AuthUser } from 'src/common/interface/auth_user.interface';
import { GroupedResponse, TimeSheetInterface } from './interface/timesheet-pdf.interface';

@Injectable()
export class TimeSheetsService {
  constructor(private prisma: PrismaService) { }

  async listTimeSheets(dto: TimeSheetsListQueryDto): Promise<PaginateResponseDto> {
    const where: any = {};

    if (dto.company_id) where.company_id = dto.company_id;
    if (dto.employee_id) where.employee_id = dto.employee_id;
    if (dto.status) where.status = dto.status;

    if (dto.start_date || dto.end_date) {
      where.period = {};
      if (dto.start_date) {
        const start = new Date(dto.start_date);
        start.setHours(0, 0, 0, 0);
        where.period.gte = start;
      }
      if (dto.end_date) {
        const end = new Date(dto.end_date);
        end.setHours(23, 59, 59, 999);
        where.period.lte = end;
      }
    }

    const page = dto.page && dto.page > 0 ? dto.page : 1;
    const limit = dto.limit && dto.limit > 0 ? dto.limit : 10;
    const skip = (page - 1) * limit;

    const [data, total_data] = await Promise.all([
      this.prisma.time_sheets.findMany({
        where,
        skip,
        take: limit,
        orderBy: { id: 'desc' },
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      }),
      this.prisma.time_sheets.count({ where }),
    ]);

    const total_pages = Math.ceil(total_data / limit);

    return {
      paginate: {
        page,
        limit,
        total_data,
        total_pages,
      },
      data,
    };
  }
  async viewTimeSheet(id: number): Promise<any> {
    let item = await this.prisma.time_sheets.findUnique({
      where: { id },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            phone: true,
            employee_number: true,
            email_address: true,
          },

        },
        company: {
          select: {
            name: true,
            registration_number: true
          }
        }
      },
    });

    if (!item) throw new NotFoundException('Time sheet not found');
    if (!item.meta) {
      const meta = await this.generateMetaWithTimeSLots(id);
      await this.prisma.time_sheets.update({
        where: { id },
        data: {
          meta: JSON.parse(JSON.stringify(meta))
        }
      });
    }
    return item;
  }
  async generateMetaWithTimeSLots(time_sheet_id: number) {
    const data = await this.prisma.schedule_log_time_slots.findMany({
      include: {
        time_slot_rates: {
          select: {
            name: true,
            rate: true,
            is_base_price: true,
          },
        },
      },
      where: {
        schedule_log: {
          time_sheet_id,
        },
      },
    });

    const resultMap = new Map<number, GroupedResponse>();

    for (const item of data) {
      const rateId = item.time_slot_rate_id ?? 0;
      const hoursWorked = Number(item.hours_worked ?? 0);
      const actualAmount = Number(item.amount ?? 0);
      const rate = Number(item.time_slot_rates?.rate ?? 0);
      const is_base_price = item.time_slot_rates?.is_base_price;
      if (!resultMap.has(rateId)) {
        resultMap.set(rateId, {
          time_slot_rate_id: rateId,
          name: item.time_slot_rates?.name ?? 'Unknown',
          total_work_hours: 0,
          base_amount: 0,
          over_time: 0,
          rate: Number(item.time_slot_rates?.rate)
        });
      }

      const group = resultMap.get(rateId)!;
      group.total_work_hours += hoursWorked;
      if (!is_base_price) {
        group.over_time += hoursWorked * rate;
        group.base_amount += actualAmount - (hoursWorked * rate);
      } else {
        group.over_time = 0
        group.base_amount += actualAmount;
      }
    }

    return Array.from(resultMap.values());
  }


  async updatePaymentDetails(id: number, dto: UpdatePaymentDto): Promise<any> {
    const { percentage } = dto;
    const item = await this.prisma.time_sheets.findUnique({ where: { id } });
    if (!item) throw new NotFoundException('Time sheet not found');
    if (item.status === time_sheet_status.paid) throw new BadRequestException('Time sheet already paid');
    const total_pay = Number(item.total_pay_worked ?? 0) + Number(item.total_pay_overtime ?? 0);
    let deduction = 0;
    let final_pay = 0
    let tax = {};
    if (percentage) {
      const taxRate = await this.prisma.tax_rates.findFirst({
        where: {
          percentage: Number(percentage),
          company_id: item.company_id
        }
      })
      if (!taxRate) throw new BadRequestException('Invalid percentage');
      deduction = +(total_pay * (percentage / 100)).toFixed(2);
      final_pay = +(total_pay - deduction).toFixed(2);
      tax = {
        percentage
      };
    }

    // Prepare tax JSON

    const updatedItem = await this.prisma.time_sheets.update({
      where: { id },
      data: {
        payment_type: dto.payment_type,
        paid_date: new Date(Date.now()),
        meta: dto.meta ?? null,
        status: time_sheet_status.paid,
        note: dto.note ?? null,
        deduction,
        final_pay,
        total_pay,
        tax_meta: tax ?? null,
      },
    });

    return updatedItem;
  }

  async getDailyAnalytics(time_sheet_id: number) {
    // Fetch logs for the given time_sheet_id
    const logs = await this.prisma.schedule_logs.findMany({
      where: { time_sheet_id, clock_out_at: { not: null } },
      select: {
        clock_out_at: true,
        duration: true,
      },
    });

    // Group by date and sum durations
    const analytics: Record<string, { date: string; day: string; total_duration: number }> = {};

    for (const log of logs) {
      if (!log.clock_out_at) continue; // Skip if null (shouldn't happen due to where clause)
      const dateObj = log.clock_out_at;
      const dateStr = dateObj.toISOString().split('T')[0];
      const dayStr = dateObj.toLocaleDateString('en-US', { weekday: 'long' });

      if (!analytics[dateStr]) {
        analytics[dateStr] = { date: dateStr, day: dayStr, total_duration: 0 };
      }
      analytics[dateStr].total_duration += log.duration ?? 0;
    }

    // Return as array sorted by date
    return Object.values(analytics).sort((a, b) => a.date.localeCompare(b.date));
  }

  async getTimeSheetsSummary(query: {
    company_id?: number;
    employee_id?: string;
    start_date?: string;
    end_date?: string;
  }) {
    const where: any = {};
    if (query.company_id) where.company_id = query.company_id;
    if (query.employee_id) where.employee_id = query.employee_id;
    if (query.start_date || query.end_date) {
      where.period = {};
      if (query.start_date) {
        const start = new Date(query.start_date);
        start.setHours(0, 0, 0, 0);
        where.period.gte = start;
      }
      if (query.end_date) {
        const end = new Date(query.end_date);
        end.setHours(23, 59, 59, 999);
        where.period.lte = end;
      }
    }

    const sheets = await this.prisma.time_sheets.findMany({
      where,
      select: {
        total_duration_worked: true,
        total_duration_overtime: true,
        total_pay_worked: true,
        total_pay_overtime: true,
        status: true,
      },
    });

    let total_duration_worked = 0;
    let total_duration_overtime = 0;
    let total_pay_worked = 0;
    let total_pay_overtime = 0;
    let paid_count = 0;
    let not_paid_count = 0;

    for (const sheet of sheets) {
      total_duration_worked += Number(sheet.total_duration_worked ?? 0);
      total_duration_overtime += Number(sheet.total_duration_overtime ?? 0);
      total_pay_worked += Number(sheet.total_pay_worked ?? 0);
      total_pay_overtime += Number(sheet.total_pay_overtime ?? 0);
      if (sheet.status === 'paid') paid_count++;
      if (sheet.status === 'not_paid') not_paid_count++;
    }

    return {
      total_duration_worked,
      total_duration_overtime,
      total_pay_worked,
      total_pay_overtime,
      paid_count,
      not_paid_count,
    };
  }
  async calculateTaxDeduction(time_sheet_id: number, percentage: number) {
    if (typeof percentage !== 'number' || isNaN(percentage) || percentage < 0) {
      throw new BadRequestException('Invalid percentage');
    }

    const sheet = await this.prisma.time_sheets.findUnique({ where: { id: time_sheet_id } });
    if (!sheet) throw new NotFoundException('Time sheet not found');
    const taxRate = await this.prisma.tax_rates.findFirst({
      where: {
        percentage: Number(percentage),
        company_id: sheet.company_id
      }
    })
    if (!taxRate) throw new BadRequestException('Invalid percentage');
    const total_pay = Number(sheet.total_pay_worked ?? 0) + Number(sheet.total_pay_overtime ?? 0);
    const deduction = +(total_pay * (percentage / 100)).toFixed(2);
    const final_pay = +(total_pay - deduction).toFixed(2);

    // Prepare tax JSON
    let tax = {
      percentage
    };


    const updated = await this.prisma.time_sheets.update({
      where: { id: time_sheet_id },
      data: {
        deduction,
        final_pay,
        tax_meta: tax,
        paid_date: new Date(Date.now()),
        // meta: dto.meta ?? null,
        status: time_sheet_status.paid,
        // note: dto.note ?? null,
        payment_type: PaymentType.DIRECT_DEPOSIT
      },
    });

    return {
      message: 'Tax calculated and updated',
      deduction,
      final_pay,
      tax,
      time_sheet_id,
    };
  }

  async findOne(id: number): Promise<any> {
    const item = await this.prisma.time_sheets.findUnique({
      where: { id },
      select: {
        id: true,
        meta: true
      }
    });
    // if (item && !item.meta) {
    const meta = await this.generateMetaWithTimeSLots(id);
    await this.prisma.time_sheets.update({
      where: { id },
      data: {
        meta: JSON.parse(JSON.stringify(meta))
      }
    });
    // }
    return await this.prisma.time_sheets.findUnique({
      where: { id },
      include: {
        employee: {
          select: {
            id: true,
            name: true,
            phone: true,
            employee_number: true,
            email_address: true,
          },

        },
        company: {
          select: {
            name: true,
            registration_number: true
          }
        }
      },
    });
  }
  async timeSheetsDataToExport(dto: TimeSheetsListQueryDto) {
    const where: any = {};

    if (dto.company_id) where.company_id = dto.company_id;
    if (dto.employee_id) where.employee_id = dto.employee_id;
    if (dto.status) where.status = dto.status;

    if (dto.start_date || dto.end_date) {
      where.period = {};
      if (dto.start_date) {
        const start = new Date(dto.start_date);
        start.setHours(0, 0, 0, 0);
        where.period.gte = start;
      }
      if (dto.end_date) {
        const end = new Date(dto.end_date);
        end.setHours(23, 59, 59, 999);
        where.period.lte = end;
      }
    }

    // const page = dto.page && dto.page > 0 ? dto.page : 1;
    // const limit = dto.limit && dto.limit > 0 ? dto.limit : 10;
    // const skip = (page - 1) * limit;

    const [data] = await Promise.all([
      this.prisma.time_sheets.findMany({
        where,
        orderBy: { id: 'desc' },
        include: {
          employee: {
            select: {
              id: true,
              name: true,
              phone: true,
            },
          },
        },
      }),
    ]);
    const transformedData = data.map((item) => ({
      'Employee': item.employee?.name || 'N/A',
      'Period': item.period ? new Date(item.period).toLocaleDateString('en-GB') : null,
      'Total Allotted Hours': (Number(item.total_duration_allotted ?? 0) / 60).toFixed(2),
      'Working Hours': (Number(item.total_duration_worked ?? 0) / 60).toFixed(2),
      'Overtime': (Number(item.total_duration_overtime ?? 0) / 60).toFixed(2),
      'Total': (Number(item.total_duration ?? 0) / 60).toFixed(2),
      'Total Pay': Number(item.total_pay),
      'Tax Deduction': Number(item.deduction),
      'Final Pay': Number(item.final_pay),
      'Status': item.status,
    }));
    // console.log(transformedData)
    return transformedData;
  }
}
