import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { CreateTimeSheetDto } from './create-time-sheet.dto';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export enum PaymentType {
  DIRECT_DEPOSIT = 'direct_deposit',
  CHEQUE = 'cheque',
  PAYPAL = 'paypal',
  WIRE_TRANSFER = 'wire_transfer',
  OTHER = 'other',
}

export class UpdateTimeSheetDto extends PartialType(CreateTimeSheetDto) { }

export class UpdatePaymentDto {
  @ApiPropertyOptional({ enum: PaymentType, description: 'Payment type (direct_deposit, cheque, paypal, wire_transfer, other)' })
  @IsNotEmpty()
  @IsEnum(PaymentType)
  payment_type?: PaymentType;

  @ApiPropertyOptional({ description: 'Any payment meta data' })
  @IsOptional()
  meta?: any;

  @ApiPropertyOptional({ description: 'Any payment note' })
  @IsOptional()
  note?: string;

  @ApiPropertyOptional({ description: 'tax percentage' })
  @IsOptional()
  percentage?: number;
}
