import { ApiProperty } from '@nestjs/swagger';

export enum TimeSheetStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid',
  // Add other statuses as needed
}

class EmployeeDto {
  @ApiProperty() id: string;
  @ApiProperty({ required: false }) name?: string;
  @ApiProperty({ required: false }) phone?: string;
}

export class TimeSheetDto {
  @ApiProperty() id: number;
  @ApiProperty() employee_id: string;
  @ApiProperty() company_id: number;
  @ApiProperty() period: string;
  @ApiProperty() total_duration_allotted: number;
  @ApiProperty() total_duration: number;
  @ApiProperty() total_duration_worked: number;
  @ApiProperty() total_duration_overtime: number;
  @ApiProperty() total_pay: number;
  @ApiProperty() total_pay_worked: number;
  @ApiProperty() total_pay_overtime: number;
  @ApiProperty({ enum: TimeSheetStatus }) status: TimeSheetStatus;
  @ApiProperty({ required: false }) payment_type?: string;
  @ApiProperty({ required: false }) paid_date?: string;
  @ApiProperty({ required: false }) meta?: any;
  @ApiProperty({ required: false }) note?: string;
  @ApiProperty() created_at: string;
  @ApiProperty({ type: EmployeeDto }) employee: EmployeeDto;
}

export class PaginateDto {
  @ApiProperty() page: number;
  @ApiProperty() limit: number;
  @ApiProperty() total_data: number;
  @ApiProperty() total_pages: number;
}

export class TimeSheetsListResponseDto {
  @ApiProperty({ type: PaginateDto })
  paginate: PaginateDto;

  @ApiProperty({ type: [TimeSheetDto] })
  data: TimeSheetDto[];
}