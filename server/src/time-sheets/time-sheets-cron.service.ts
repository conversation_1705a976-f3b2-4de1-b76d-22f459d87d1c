import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class TimeSheetsCronService {
  private readonly logger = new Logger(TimeSheetsCronService.name);

  constructor(private readonly prisma: PrismaService) { }

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async handleDailyTimeSheetUpdate() {
    this.logger.log('Running daily timesheet update...');

    // 1. Find all schedule_logs that need to be processed
    const logs = await this.prisma.schedule_logs.findMany({
      where: {
        time_sheet_updated: false,
        status: 'approved',
      },
      select: {
        id: true,
        employee_id: true,
        company_id: true,
        work_pay: true,
        overtime_pay: true,
        total_pay: true,
        created_at: true,
        work_duration: true,
        overtime_duration: true,
        duration: true,
        extra_pay: true,
        schedules: {
          select: {
            duration: true
          },
        },
      },
    });

    // 2. Group logs by employee_id and company_id
    const grouped: Record<string, typeof logs> = {};
    for (const log of logs) {
      if (!log.employee_id || !log.company_id) continue;
      const key = `${log.company_id}_${log.employee_id}`;
      if (!grouped[key]) grouped[key] = [];
      grouped[key].push(log);
    }

    // 3. For each group, sum up the amounts and update/create timesheet
    for (const key of Object.keys(grouped)) {
      const [company_id, employee_id] = key.split('_');
      const groupLogs = grouped[key];

      const total_work_pay = groupLogs.reduce((sum, l) => sum + Number(l.work_pay || 0), 0);
      const total_overtime_pay = groupLogs.reduce((sum, l) => sum + Number(l.overtime_pay || 0), 0);
      const total_pay = groupLogs.reduce((sum, l) => sum + Number(l.total_pay || 0), 0);

      // You can set the period as today, or calculate based on your needs
      const period = new Date(); // e.g., today

      // Try to update, if not found then create
      let timeSheet = await this.prisma.time_sheets.findFirst({
        where: {
          employee_id,
          company_id: Number(company_id),
          period,
        },
      });

      if (timeSheet) {
        await this.prisma.time_sheets.update({
          where: { id: timeSheet.id },
          data: {
            total_pay: { increment: total_pay },
            total_pay_worked: { increment: total_work_pay },
            final_pay: { increment: total_work_pay },
            total_pay_overtime: { increment: total_overtime_pay },
            total_duration_allotted: { increment: groupLogs.reduce((sum, l) => sum + Number(l.schedules?.duration || 0), 0) },
            total_duration_worked: { increment: groupLogs.reduce((sum, l) => sum + Number(l.work_duration || 0), 0) },
            total_duration_overtime: { increment: groupLogs.reduce((sum, l) => sum + Number(l.overtime_duration || 0), 0) },
            total_duration: { increment: groupLogs.reduce((sum, l) => sum + Number(l.duration || 0), 0) },
            extra_pay_total: { increment: groupLogs.reduce((sum, l) => sum + Number(l.extra_pay || 0), 0) },
          },
        });
      } else {
        const latestTimeSheet = await this.prisma.time_sheets.findFirst({
          where: {
            employee_id,
            company_id: Number(company_id),
          },
          select: {
            period_to: true
          },
          orderBy: {
            created_at: 'desc'
          }
        });
        timeSheet = await this.prisma.time_sheets.create({
          data: {
            employee_id,
            company_id: Number(company_id),
            period,
            total_pay: total_pay,
            final_pay: total_pay,
            total_pay_worked: total_work_pay,
            total_pay_overtime: total_overtime_pay,
            total_duration_allotted: groupLogs.reduce((sum, l) => sum + Number(l.schedules?.duration || 0), 0),
            total_duration_worked: groupLogs.reduce((sum, l) => sum + Number(l.work_duration || 0), 0),
            total_duration_overtime: groupLogs.reduce((sum, l) => sum + Number(l.overtime_duration || 0), 0),
            total_duration: groupLogs.reduce((sum, l) => sum + Number(l.duration || 0), 0),
            extra_pay_total: groupLogs.reduce((sum, l) => sum + Number(l.extra_pay || 0), 0),
            period_to: period,
            period_from: latestTimeSheet?.period_to ? new Date(latestTimeSheet?.period_to) : new Date(
              new Date(period).getFullYear(),
              new Date(period).getMonth() - 1,
              1
            ),
          },
        });
      }

      // 4. Mark schedule_logs as updated and set time_sheet_id
      await this.prisma.schedule_logs.updateMany({
        where: {
          id: { in: groupLogs.map(l => l.id) },
        },
        data: {
          time_sheet_updated: true,
          time_sheet_id: timeSheet.id,
        },
      });
    }

    this.logger.log('TimeSheet update complete.');
  }
}