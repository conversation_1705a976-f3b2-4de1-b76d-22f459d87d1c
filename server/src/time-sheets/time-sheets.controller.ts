import { Controller, Get, Query, Request, UseGuards, Param, Body, Patch, Res, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiOkResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { TimeSheetsService } from './time-sheets.service';
import { TimeSheetsCronService } from './time-sheets-cron.service';
import { timeSheetsListExample } from './docs/time-sheets-swagger.examples';
import { TimeSheetsListResponseDto, TimeSheetDto } from './dto/time-sheets-list.dto';
import { TimeSheetsListQueryDto } from './dto/time-sheets-list.query.dto';
import { UpdatePaymentDto } from './dto/update-time-sheet.dto';
import { timeSheetNotFoundErrorExample } from './docs/time-sheets-swagger.errors';
import { Response } from 'express';
import { generateTimeSheetPdf } from './pdf.utils';
import { ExportService } from 'src/export/export.service';

@ApiTags('Time Sheets')
// @UseGuards(JwtAuthGuard, RolesGuard)
@Controller('time-sheets')
export class TimeSheetsController {
  constructor(
    private readonly timeSheetsService: TimeSheetsService,
    private readonly timeSheetsCronService: TimeSheetsCronService,
    private readonly excelExportService: ExportService
  ) { }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('company_admin')
  @ApiOperation({ summary: 'List time sheets', description: 'List all time sheets with filters and pagination.' })
  @ApiResponse({
    status: 200,
    description: 'List of time sheets.',
    type: TimeSheetsListResponseDto,
    schema: { example: timeSheetsListExample },
  })
  async listTimeSheets(@Request() req, @Query() query: TimeSheetsListQueryDto): Promise<TimeSheetsListResponseDto> {
    query.company_id = Number(req.user.company_id);

    return this.timeSheetsService.listTimeSheets(query);
  }

  @Get('export')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('company_admin')
  @ApiOperation({ summary: 'export time sheets', description: 'Export all time sheets with filters.' })

  async exportTimeSheets(@Request() req, @Query() query: TimeSheetsListQueryDto, @Res() res: Response) {
    query.company_id = Number(req.user.company_id);

    const data = await this.timeSheetsService.timeSheetsDataToExport(query);
    return this.excelExportService.exportToExcel(data, res, 'time-sheets.xlsx');

  }

  @Get('cron')
  @UseGuards(JwtAuthGuard, RolesGuard)

  runCron() {
    return this.timeSheetsCronService.handleDailyTimeSheetUpdate();
  }

  @Get('employee')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @Roles('employee')
  @ApiOperation({ summary: 'List time sheets for employee', description: 'List all time sheets for the logged-in employee.' })
  @ApiResponse({
    status: 200,
    description: 'List of time sheets for employee.',
    type: TimeSheetsListResponseDto,
    schema: { example: timeSheetsListExample },
  })
  async listEmployeeTimeSheets(@Request() req, @Query() query: TimeSheetsListQueryDto): Promise<TimeSheetsListResponseDto> {
    // Always use employee_id from token, ignore any from query
    query.employee_id = req.user.employee_id;
    return this.timeSheetsService.listTimeSheets(query);
  }
  @Get('stats')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @ApiOperation({
    summary: 'Get summary stats for time sheets',
    description: 'Returns sum of total_duration_worked, total_duration_overtime, total_pay_worked, total_pay_overtime, and count of paid/not_paid statuses. Supports optional filters (employee_id, start_date, end_date).',
  })
  @ApiOkResponse({
    description: 'Summary statistics for time sheets.',
    schema: {
      example: {
        total_duration_worked: 12000,
        total_duration_overtime: 1500,
        total_pay_worked: 50000,
        total_pay_overtime: 8000,
        paid_count: 12,
        not_paid_count: 5,
      },
    },
  })
  async getTimeSheetsSummary(
    @Request() req,
    @Query() query: { employee_id?: string; start_date?: string; end_date?: string }
  ) {
    // Always use company_id from token
    return this.timeSheetsService.getTimeSheetsSummary({
      ...query,
      company_id: req.user.company_id ? Number(req.user.company_id) : undefined,
    });
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @Roles('company_admin', 'employee')
  @ApiOperation({ summary: 'View time sheet', description: 'Get a single time sheet by ID.' })
  @ApiParam({ name: 'id', type: Number, description: 'Time sheet ID' })
  @ApiResponse({
    status: 200,
    description: 'Time sheet details.',
    type: TimeSheetDto,
    schema: { example: timeSheetsListExample.data[0] },
  })
  @ApiResponse({
    status: 404,
    description: 'Time sheet not found.',
    schema: { example: timeSheetNotFoundErrorExample },
  })
  async viewTimeSheet(@Param('id') id: number): Promise<TimeSheetDto> {
    return this.timeSheetsService.viewTimeSheet(id);
  }

  @Patch(':id/record-payment')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @Roles('company_admin')
  @ApiOperation({ summary: 'Update payment details', description: 'Update payment details for a time sheet.' })
  @ApiParam({ name: 'id', type: Number, description: 'Time sheet ID' })
  @ApiBody({ type: UpdatePaymentDto })
  @ApiResponse({
    status: 200,
    description: 'Updated time sheet.',
    type: TimeSheetDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Time sheet not found.',
    schema: { example: timeSheetNotFoundErrorExample },
  })
  async updatePaymentDetails(
    @Param('id') id: number,
    @Body() dto: UpdatePaymentDto,
  ): Promise<TimeSheetDto> {
    return this.timeSheetsService.updatePaymentDetails(id, dto);
  }

  @Get(':id/daily-analytics')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @ApiOperation({
    summary: 'Get daily analytics for a time sheet',
    description: 'Returns date, day, and total duration for each day in the time sheet (grouped by date).',
  })
  @ApiParam({ name: 'id', type: Number, description: 'Time sheet ID' })
  @ApiResponse({
    status: 200,
    description: 'Daily analytics for the time sheet.',
    schema: {
      example: [
        { date: '2025-05-22', day: 'Thursday', total_duration: 480 },
        { date: '2025-05-23', day: 'Friday', total_duration: 510 },
      ],
    },
  })
  async getDailyAnalytics(@Param('id') time_sheet_id: number) {
    return this.timeSheetsService.getDailyAnalytics(time_sheet_id);
  }

  @Patch(':time_sheet_id/calculate-tax')
  @UseGuards(JwtAuthGuard, RolesGuard)

  @ApiOperation({ summary: 'Calculate tax deduction for a time sheet' })
  @ApiParam({ name: 'time_sheet_id', type: Number })
  @ApiBody({ schema: { properties: { percentage: { type: 'number', example: 10 } } } })
  @ApiResponse({ status: 200, description: 'Tax calculated and updated' })
  async calculateTax(
    @Param('time_sheet_id') time_sheet_id: number,
    @Body('percentage') percentage: number
  ) {
    return this.timeSheetsService.calculateTaxDeduction(time_sheet_id, percentage);
  }

  @Get(':id/pdf')
  async getTimeSheetPdf(@Param('id') id: string, @Res() res: Response) {
    const sheet = await this.timeSheetsService.findOne(Number(id));
    if (!sheet) throw new NotFoundException('Time sheet not found');

    generateTimeSheetPdf(sheet, res);
  }

}
