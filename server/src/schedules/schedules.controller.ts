import { Controller, Get, Post, Body, Param, Put, Delete, ParseIntPipe, UseGuards, Request, Query, ForbiddenException } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SchedulesService } from './schedules.service';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { ScheduleListResponseDto, ScheduleDto, scheduleCreateResponseDto } from './dto/schedule-list.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { ResponseDto } from 'src/common/dto/response.dto';

@ApiTags('schedules')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('schedules')
export class SchedulesController {
  constructor(private readonly schedulesService: SchedulesService) { }

  @Get()
  @ApiOperation({ summary: 'List schedules', description: 'Returns a list of schedules.' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of schedules',
    type: ScheduleListResponseDto,
  })
  async listSchedules(@Request() req, @Query() filter: any): Promise<ScheduleListResponseDto> {
    return this.schedulesService.listSchedules(req.user, filter);
  }

  @Get('employee')
  @Roles('employee')
  @ApiOperation({ summary: 'List schedules for employee', description: 'Returns a list of schedules for the logged-in employee.' })
  @ApiResponse({
    status: 200,
    description: 'List of schedules for employee.',
    type: ScheduleListResponseDto,
  })
  @ApiResponse({ status: 403, description: 'Forbidden. Only employees can access this endpoint.' })
  async listSchedulesForEmployee(@Request() req, @Query() filter: any): Promise<ScheduleListResponseDto> {
    if (req.user.role !== 'employee') {
      throw new ForbiddenException('Only employees can access this endpoint.');
    }
    // Always use employee_id from token, ignore any from query
    filter.employee_id = req.user.employee_id;
    return this.schedulesService.listSchedulesForEmployee(req.user, filter);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get schedule', description: 'Get a schedule by ID.' })
  @ApiResponse({
    status: 200,
    description: 'Schedule found.',
    type: ScheduleDto,
  })
  @ApiResponse({ status: 404, description: 'Schedule not found' })
  async getSchedule(@Param('id', ParseIntPipe) id: number): Promise<ScheduleDto> {
    return this.schedulesService.getSchedule(id);
  }

  @Post()
  @Roles('company_admin', 'super_admin','company_staff')
  @ApiOperation({ summary: 'Create schedule', description: 'Create a new schedule.' })
  @ApiResponse({
    status: 201,
    description: 'Schedule created successfully',
    type: scheduleCreateResponseDto,
  })
  async createSchedule(@Request() req, @Body() dto: CreateScheduleDto): Promise<ResponseDto> {
    return this.schedulesService.createSchedule(dto, req.user);
  }

  @Put(':id')
  @Roles('company_admin', 'super_admin','company_staff')
  @ApiOperation({ summary: 'Update schedule', description: 'Update an existing schedule.' })
  @ApiResponse({
    status: 200,
    description: 'Schedule updated successfully',
    type: ScheduleDto,
  })
  @ApiResponse({ status: 404, description: 'Schedule not found' })
  async updateSchedule(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateScheduleDto): Promise<ScheduleDto> {
    return this.schedulesService.updateSchedule(id, dto);
  }

  @Delete(':id')
  @Roles('company_admin', 'super_admin','company_staff')
  @ApiOperation({ summary: 'Delete schedule', description: 'Delete a schedule by ID.' })
  @ApiResponse({
    status: 200,
    description: 'Schedule deleted successfully.',
    schema: { example: { message: 'Schedule deleted successfully' } },
  })
  @ApiResponse({ status: 404, description: 'Schedule not found' })
  async deleteSchedule(@Param('id', ParseIntPipe) id: number) {
    return this.schedulesService.deleteSchedule(id);
  }

}
