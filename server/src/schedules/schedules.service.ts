import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import { format, parse } from 'date-fns';
import { AuthUser } from 'src/common/interface/auth_user.interface';
import { ResponseDto } from 'src/common/dto/response.dto';
import { EngagespotService } from 'src/engagespot/engagespot.service';
import { schedule_status } from '@prisma/client';

@Injectable()
export class SchedulesService {
  constructor(
    private readonly prisma: PrismaService,
    private engagespotService: EngagespotService,
  ) { }

  async listSchedules(
    user: AuthUser,
    filters: {
      employee_id?: string;
      start_date?: string;
      end_date?: string;
      created_by?: string
    },
  ): Promise<any> {
    // Build Prisma where clause dynamically based on filters
    const where: any = { company_id: Number(user.company_id) };

    if (filters.employee_id) {
      where.employee_id = filters.employee_id;
    }
    if (filters.start_date) {
      where.start_date = { gte: new Date(filters.start_date) };
    }
    if (filters.end_date) {
      where.end_date = { lte: new Date(filters.end_date) };
    }

    if (filters.created_by) {
      where.created_by = (filters.created_by);
    }

    const schedules = await this.prisma.schedules.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            raw_user_meta_data: true,

          }
        },
        employee: {
          select: {
            name: true,
            id: true,
          },

        },
      },
      orderBy: {
        id: 'desc',
      },
    });
    const total = await this.prisma.schedules.count({ where });
    return {
      schedules: schedules.map((schedule) => ({
        ...schedule,
        start_time: schedule.start_time
          ? format(
            typeof schedule.start_time === 'string'
              ? new Date(schedule.start_time)
              : schedule.start_time,
            'hh:mm a',
          )
          : null,
        end_time: schedule.end_time
          ? format(
            typeof schedule.end_time === 'string'
              ? new Date(schedule.end_time)
              : schedule.end_time,
            'hh:mm a',
          )
          : null,
        start_date: schedule.start_date
          ? typeof schedule.start_date === 'string'
            ? (schedule.start_date as string).slice(0, 10)
            : (schedule.start_date as Date).toISOString().slice(0, 10)
          : null,
        end_date: schedule.end_date
          ? typeof schedule.end_date === 'string'
            ? (schedule.end_date as string).slice(0, 10)
            : (schedule.end_date as Date).toISOString().slice(0, 10)
          : null,
      })),
      total,
    };
  }
  async listSchedulesForEmployee(
    user: AuthUser,
    filters: {
      employee_id?: string;
      start_date?: string;
      end_date?: string;
    },
  ): Promise<any> {
    // Build Prisma where clause dynamically based on filters
    const where: any = { company_id: Number(user.company_id) };

    if (filters.employee_id) {
      where.employee_id = filters.employee_id;
    }
    if (filters.start_date) {
      where.start_date = { gte: new Date(filters.start_date) };
    }
    if (filters.end_date) {
      where.end_date = { lte: new Date(filters.end_date) };
    }

    const schedules = await this.prisma.schedules.findMany({
      where,
      include: {
        employee: {
          select: {
            name: true,
            id: true,
          },
        },
        schedule_logs: {
          select: {
            id: true,
            status: true,
            clock_in_at: true,
            clock_out_at: true,
            employee_id: true,
          },
          where: {
            employee_id: filters.employee_id,
          },
        },
      },
      orderBy: {
        start_date: 'asc',
      },
    });
    const total = await this.prisma.schedules.count({ where });

    return {
      schedules: schedules.map((schedule) => ({
        ...schedule,
        start_time: schedule.start_time
          ? format(
            typeof schedule.start_time === 'string'
              ? new Date(schedule.start_time)
              : schedule.start_time,
            'hh:mm a',
          )
          : null,
        end_time: schedule.end_time
          ? format(
            typeof schedule.end_time === 'string'
              ? new Date(schedule.end_time)
              : schedule.end_time,
            'hh:mm a',
          )
          : null,
        start_date: schedule.start_date
          ? typeof schedule.start_date === 'string'
            ? (schedule.start_date as string).slice(0, 10)
            : (schedule.start_date as Date).toISOString().slice(0, 10)
          : null,
        end_date: schedule.end_date
          ? typeof schedule.end_date === 'string'
            ? (schedule.end_date as string).slice(0, 10)
            : (schedule.end_date as Date).toISOString().slice(0, 10)
          : null,
      })),
      total,
    };
  }
  private toScheduleDto(schedule: any) {
    return {
      ...schedule,
      start_time: schedule.start_time
        ? format(
          typeof schedule.start_time === 'string'
            ? new Date(schedule.start_time)
            : schedule.start_time,
          'hh:mm a',
        )
        : null,
      end_time: schedule.end_time
        ? format(
          typeof schedule.end_time === 'string'
            ? new Date(schedule.end_time)
            : schedule.end_time,
          'hh:mm a',
        )
        : null,
    };
  }

  async getSchedule(id: number) {
    const schedule = await this.prisma.schedules.findUnique({
      where: { id },
      include: {
        employee: {
          select: {
            name: true,
            id: true,
          },
        },
      },
    });
    if (!schedule) throw new NotFoundException('Schedule not found');
    return this.toScheduleDto(schedule);
  }

  async createSchedule(dto: CreateScheduleDto, authUser: AuthUser): Promise<ResponseDto> {
    let parsedStartTime: Date | undefined;
    let parsedEndTime: Date | undefined;

    if (dto.start_time) {
      parsedStartTime = parse(dto.start_time, 'hh:mm a', new Date(0));
      if (isNaN(parsedStartTime.getTime())) {
        throw new Error('Invalid start_time format. Use "hh:mm AM/PM"');
      }
    }

    if (dto.end_time) {
      parsedEndTime = parse(dto.end_time, 'hh:mm a', new Date(0));
      if (isNaN(parsedEndTime.getTime())) {
        throw new Error('Invalid end_time format. Use "hh:mm AM/PM"');
      }
    }

    if (!dto.start_date || !dto.end_date) {
      throw new Error('Invalid date range. Please provide start and end dates.');
    }

    const start_date = new Date(dto.start_date);
    const end_date = new Date(dto.end_date);
    const schedulesToCreate: CreateScheduleDto[] = [];
    let currentDate = new Date(start_date);

    while (currentDate <= end_date) {
      const scheduleStart = this.combineDateTime(currentDate, parsedStartTime);
      let scheduleEnd = this.combineDateTime(currentDate, parsedEndTime);
      if (scheduleEnd < scheduleStart) {
        const nextDate = new Date(currentDate);
        nextDate.setDate(nextDate.getDate() + 1);
        scheduleEnd = this.combineDateTime(nextDate, parsedEndTime);
      }
      const duration = this.calculateDuration(scheduleStart, scheduleEnd);
      schedulesToCreate.push({
        start_date: scheduleStart.toISOString(),
        end_date: scheduleEnd.toISOString(),
        is_finished: false,
        created_by: authUser.sub,
        duration,
        ...(dto.company_id && { company_id: dto.company_id }),
        ...(dto.employee_id && { employee_id: dto.employee_id }),
        ...(dto.type && { type: dto.type }),
        ...(dto.note && { note: dto.note }),
        ...(parsedStartTime && { start_time: scheduleStart.toISOString() }),
        ...(parsedEndTime && { end_time: scheduleEnd.toISOString() }),
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    await this.prisma.schedules.createMany({
      data: schedulesToCreate,
    });

    await this.sendScheduleNotification(dto.employee_id, dto);
    return {
      status: true,
      message: 'schedules created',
    };
  }

  async updateSchedule(id: number, dto: UpdateScheduleDto) {
    const schedule = await this.prisma.schedules.findUnique({ where: { id } });
    if (!schedule) throw new NotFoundException('Schedule not found');
    if (schedule.status != schedule_status.assigned) throw new BadRequestException('Cannot delete schedule with logs');

    let parsedStartTime: Date | undefined;
    let parsedEndTime: Date | undefined;

    if (dto.start_time) {
      parsedStartTime = parse(dto.start_time, 'hh:mm a', new Date(0));
      if (isNaN(parsedStartTime.getTime())) {
        throw new BadRequestException('Invalid start_time format. Use "hh:mm AM/PM"');
      }
    }
    if (dto.end_time) {
      parsedEndTime = parse(dto.end_time, 'hh:mm a', new Date(0));
      if (isNaN(parsedEndTime.getTime())) {
        throw new BadRequestException('Invalid end_time format. Use "hh:mm AM/PM"');
      }
    }

    if (!dto.start_date || !dto.end_date) {
      throw new Error('Invalid date range. Please provide start and end dates.');
    }

    const baseStartDate = new Date(dto.start_date);
    const baseEndDate = new Date(dto.start_date);
    const scheduleStart = this.combineDateTime(baseStartDate, parsedStartTime);
    let scheduleEnd = this.combineDateTime(baseEndDate, parsedEndTime);
    if (scheduleEnd < scheduleStart) {
      const nextDate = new Date(baseEndDate);
      nextDate.setDate(nextDate.getDate() + 1);
      scheduleEnd = this.combineDateTime(nextDate, parsedEndTime);
    }
    const duration = this.calculateDuration(scheduleStart, scheduleEnd);

    const updated = await this.prisma.schedules.update({
      where: { id },
      data: {
        ...dto,
        start_date: scheduleStart,
        end_date: scheduleEnd,
        ...(parsedStartTime && { start_time: parsedStartTime }),
        ...(parsedEndTime && { end_time: parsedEndTime }),
        duration,
      },
    });
    return this.toScheduleDto(updated);
  }

  // --- Private helpers ---
  private combineDateTime(date: Date, time: Date | undefined): Date {
    if (!time) return date;
    const combined = new Date(date);
    combined.setHours(time.getHours(), time.getMinutes(), 0, 0);
    return combined;
  }

  private calculateDuration(
    start: Date | undefined,
    end: Date | undefined,
  ): number | undefined {
    if (!start || !end) return undefined;
    return Math.floor((end.getTime() - start.getTime()) / 60000);
  }

  async deleteSchedule(id: number) {
    const schedule = await this.prisma.schedules.findUnique({ where: { id } });
    if (!schedule) throw new NotFoundException('Schedule not found');
    if (schedule.status != schedule_status.assigned) throw new BadRequestException('Cannot delete schedule with logs');
    await this.prisma.schedules.delete({ where: { id } });
    return { message: 'Schedule deleted successfully' };
  }

  async sendScheduleNotification(employee_id?: string, dto?: CreateScheduleDto) {
    if (!employee_id) return;
    const employee = await this.prisma.employee.findUnique({ where: { id: employee_id } });
    if (employee) {
      await this.engagespotService.createOrUpdateUser(employee.id, {
        mobile: employee.phone ?? undefined,
        name: employee.name ?? undefined,
        email: employee.email_address ?? undefined,
      });
      await this.engagespotService.send('job_scheduled', [employee.id], {
        employee_name: employee.name,
        start_date: dto?.start_date,
        end_date: dto?.end_date,
        start_time: dto?.start_time,
        end_time: dto?.end_time,
      });
    }
  }
}
