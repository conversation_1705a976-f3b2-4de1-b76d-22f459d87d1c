import { ApiProperty } from '@nestjs/swagger';
import { EmployeeDto } from 'src/employees/dto/employee-list.dto';
export class EmployeeScheduleDto {
  @ApiProperty() id: string;

  @ApiProperty() name: string;
}

export class UserSchedule {
  @ApiProperty() id: string;

  @ApiProperty() email: string;
}
export class ScheduleDto {
  @ApiProperty({ description: 'Schedule ID' })
  id: number;

  @ApiProperty({ description: 'Company ID', nullable: true })
  company_id: number | null;

  @ApiProperty({ description: 'Employee ID', required: false, nullable: true })
  employee_id?: string | null;

  @ApiProperty({ description: 'Created User ID', required: false, nullable: true })
  created_by?: string | null;

  @ApiProperty({ description: 'Schedule type', required: false, nullable: true })
  type?: string | null;

  @ApiProperty({ description: 'Is finished', required: false, nullable: true })
  is_finished?: boolean | null;

  @ApiProperty({ description: 'Note', required: false, nullable: true })
  note?: string | null;

  @ApiProperty({ description: 'Start date', required: false, nullable: true })
  start_date?: Date | null;

  @ApiProperty({ description: 'End date', required: false, nullable: true })
  end_date?: Date | null;

  @ApiProperty({ description: 'Start time', required: false, nullable: true })
  start_time?: string | null;

  @ApiProperty({ description: 'End time', required: false, nullable: true })
  end_time?: string | null;

  @ApiProperty({ description: 'Created at' })
  created_at: Date;

  @ApiProperty() employee: EmployeeScheduleDto;
  @ApiProperty() user: UserSchedule;

}

export class ScheduleListResponseDto {
  @ApiProperty({ description: 'List of schedules', type: [ScheduleDto] })
  schedules: ScheduleDto[];

  @ApiProperty({ description: 'Total count of schedules' })
  total: number;
}

export class scheduleCreateResponseDto {
  @ApiProperty({ description: 'Status of the request' })
  status: boolean;

  @ApiProperty({ description: 'Message' })
  message: string;
}