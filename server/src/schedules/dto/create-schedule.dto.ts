import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, IsBoolean, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export enum ScheduleType {
  single_day = 'single_day',
  weekly = 'weekly',
  monthly = 'monthly',
}

export class CreateScheduleDto {
  @ApiProperty({ description: 'Company ID', example: 1 })
  @IsInt({ message: 'Company ID must be an integer' })
  @Type(() => Number)
  company_id?: number;

  @ApiProperty({ description: 'Employee ID (UUID)', example: 'uuid-string', required: false })
  @IsOptional()
  @IsUUID('4', { message: 'Employee ID must be a valid UUID' })
  employee_id?: string;

  @ApiProperty({ description: 'Schedule type', enum: ScheduleType, required: false })
  @IsOptional()
  @IsEnum(ScheduleType, { message: 'Type must be one of: single_day, weekly, monthly' })
  type?: ScheduleType;

  @ApiProperty({ description: 'Is schedule finished?', default: false, required: false })
  @IsOptional()
  @IsBoolean({ message: 'is_finished must be a boolean' })
  is_finished?: boolean;

  @ApiProperty({ description: 'Note', required: false })
  @IsOptional()
  @IsString({ message: 'Note must be a string' })
  note?: string;

  @ApiProperty({ description: 'Start date (YYYY-MM-DD)', required: false })
  @IsOptional()
  // @IsDateString({}, { message: 'start_date must be a valid date string' })
  start_date?: string;

  @ApiProperty({ description: 'End date (YYYY-MM-DD)', required: false })
  @IsOptional()
  // @IsDateString({}, { message: 'end_date must be a valid date string' })
  end_date?: string;

  @ApiProperty({ description: 'Start time (HH:mm:ss)', required: false })
  @IsOptional()
  @IsString({ message: 'start_time must be a string' })
  // @IsDateString({}, { message: 'start_time must be a valid time string' })
  start_time?: string;

  @ApiProperty({ description: 'End time (HH:mm:ss)', required: false })
  @IsOptional()
  @IsString({ message: 'end_time must be a string' })
  // @IsDateString({}, { message: 'end_time must be a valid time string' })
  end_time?: string;

  @ApiProperty({ description: 'Duration in minutes', required: false })
  @IsOptional()
  @IsInt({ message: 'Duration must be an integer' })
  duration?: number;

  @IsOptional()
  created_by: string
}
