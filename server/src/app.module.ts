import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { PrismaModule } from './prisma/prisma.module';
import { EmployeesModule } from './employees/employees.module';
import { TimeSlotsModule } from './time-slots/time-slots.module';
import { CompaniesModule } from './companies/companies.module';
import { SchedulesModule } from './schedules/schedules.module';
import { ScheduleLogsModule } from './schedule-logs/schedule-logs.module';
import { TimeSheetsModule } from './time-sheets/time-sheets.module';
import { EngagespotModule } from './engagespot/engagespot.module';
import { TaxRatesModule } from './tax-rates/tax-rates.module';
import { ExportModule } from './export/export.module';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(), // <-- Important
    PrismaModule,
    AuthModule,
    EmployeesModule,
    TimeSlotsModule,
    CompaniesModule,
    SchedulesModule,
    ScheduleLogsModule,
    TimeSheetsModule,
    EngagespotModule,
    TaxRatesModule,
    ExportModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
