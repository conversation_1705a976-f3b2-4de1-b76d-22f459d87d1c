import { Module } from '@nestjs/common';
import { TaxRatesService } from './tax-rates.service';
import { TaxRatesController } from './tax-rates.controller';
import { PrismaService } from '../prisma/prisma.service';
import { SupabaseModule } from 'src/supabase/supabase.module';
import { PrismaModule } from 'src/prisma/prisma.module';

@Module({
  imports: [PrismaModule, SupabaseModule],
  controllers: [TaxRatesController],
  providers: [TaxRatesService, PrismaService],
})
export class TaxRatesModule { }
