import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsOptional } from 'class-validator';

export class CreateTaxRateDto {
  @ApiProperty({ example: 1, description: 'Company ID' })
  @IsNotEmpty()
  @IsNumber()
  company_id: number;

  @ApiProperty({ example: 10.5, description: 'Tax percentage (0-100)' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Max(100)
  percentage: number;
}