import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTaxRateDto } from './dto/create-tax-rate.dto';
import { UpdateTaxRateDto } from './dto/update-tax-rate.dto';

@Injectable()
export class TaxRatesService {
  constructor(private prisma: PrismaService) { }

  async create(createTaxRateDto: CreateTaxRateDto, user: { company_id: number }) {
    // Check if percentage already exists for this company
    const exists = await this.prisma.tax_rates.findFirst({
      where: {
        company_id: Number(user.company_id),
        percentage: createTaxRateDto.percentage,
      },
    });
    if (exists) {
      throw new BadRequestException('This tax rate already exists for your company.');
    }

    const created = await this.prisma.tax_rates.create({
      data: {
        company_id: Number(user.company_id),
        percentage: createTaxRateDto.percentage,
      },
    });
    return created;
  }

  async findAll(user: { company_id: number }) {
    const [items] = await Promise.all([
      this.prisma.tax_rates.findMany({
        where: { company_id: Number(user.company_id) },
        orderBy: { percentage: 'asc' },
      }),

    ]);
    return { items, total: items.length };
  }

  async findOne(id: number, user: { company_id: number }) {
    const taxRate = await this.prisma.tax_rates.findUnique({ where: { id } });
    if (!taxRate) throw new NotFoundException('Tax rate not found');
    return taxRate;
  }

  async update(id: number, updateTaxRateDto: UpdateTaxRateDto, user: { company_id: number }) {
    const taxRate = await this.prisma.tax_rates.findUnique({ where: { id } });
    if (!taxRate) throw new NotFoundException('Tax rate not found');

    // If updating percentage, check for duplicates
    if (
      updateTaxRateDto.percentage !== undefined &&
      Number(updateTaxRateDto.percentage) !== Number(taxRate.percentage)
    ) {
      const exists = await this.prisma.tax_rates.findFirst({
        where: {
          company_id: Number(user.company_id),
          percentage: updateTaxRateDto.percentage,
          id: { not: id },
        },
      });
      if (exists) {
        throw new BadRequestException('This tax rate already exists for your company.');
      }
    }

    const updated = await this.prisma.tax_rates.update({
      where: { id },
      data: updateTaxRateDto,
    });
    return updated;
  }

  async remove(id: number, user: { company_id: number }) {
    const taxRate = await this.prisma.tax_rates.findUnique({ where: { id } });
    if (!taxRate) throw new NotFoundException('Tax rate not found');
    await this.prisma.tax_rates.delete({ where: { id } });
    return { message: 'Tax rate deleted successfully' };
  }
}
