import { Controller, Get, Post, Body, Patch, Param, Delete, Req, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { TaxRatesService } from './tax-rates.service';
import { CreateTaxRateDto } from './dto/create-tax-rate.dto';
import { UpdateTaxRateDto } from './dto/update-tax-rate.dto';
import { TaxRateDto } from './dto/tax-rate.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('tax-rates')
@ApiBearerAuth()
@Controller('tax-rates')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TaxRatesController {
  constructor(private readonly taxRatesService: TaxRatesService) {}

  @Get()
  @Roles('company_admin', 'super_admin')
  @ApiOperation({ summary: 'List tax rates', description: 'List all tax rates for the authenticated user\'s company.' })
  @ApiResponse({ status: 200, description: 'List of tax rates', type: [TaxRateDto] })
  async findAll(@Req() req: any) {
    return this.taxRatesService.findAll(req.user);
  }

  @Post()
  @Roles('company_admin', 'super_admin')
  @ApiOperation({ summary: 'Create tax rate', description: 'Create a new tax rate for the authenticated user\'s company.' })
  @ApiResponse({ status: 201, description: 'Tax rate created', type: TaxRateDto })
  async create(@Req() req: any, @Body() createTaxRateDto: CreateTaxRateDto) {
    return this.taxRatesService.create(createTaxRateDto, req.user);
  }

  @Get(':id')
  @Roles('company_admin', 'super_admin')
  @ApiOperation({ summary: 'Get tax rate by ID' })
  @ApiResponse({ status: 200, description: 'Tax rate found', type: TaxRateDto })
  @ApiResponse({ status: 404, description: 'Tax rate not found' })
  async findOne(@Param('id') id: string, @Req() req: any) {
    return this.taxRatesService.findOne(Number(id), req.user);
  }

  @Patch(':id')
  @Roles('company_admin', 'super_admin')
  @ApiOperation({ summary: 'Update tax rate' })
  @ApiResponse({ status: 200, description: 'Tax rate updated', type: TaxRateDto })
  @ApiResponse({ status: 404, description: 'Tax rate not found' })
  async update(@Param('id') id: string, @Body() updateTaxRateDto: UpdateTaxRateDto, @Req() req: any) {
    return this.taxRatesService.update(Number(id), updateTaxRateDto, req.user);
  }

  @Delete(':id')
  @Roles('company_admin', 'super_admin')
  @ApiOperation({ summary: 'Delete tax rate' })
  @ApiResponse({ status: 200, description: 'Tax rate deleted' })
  @ApiResponse({ status: 404, description: 'Tax rate not found' })
  async remove(@Param('id') id: string, @Req() req: any) {
    return this.taxRatesService.remove(Number(id), req.user);
  }
}
