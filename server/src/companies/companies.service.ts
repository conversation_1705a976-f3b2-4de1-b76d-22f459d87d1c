import { Injectable, UnauthorizedException, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CompanyListResponseDto, CompanyDto } from './dto/company-list.dto';
import { CreateCompanyDto } from './dto/create-company.dto';
import { AuthUser } from 'src/common/interface/auth_user.interface';
import { SupabaseService } from 'src/supabase/supabase.service';
import { CreateCompanyAdminDto, EditCompanyAdminDto } from './dto/company-admin.dto';

@Injectable()
export class CompaniesService {
  constructor(private readonly prisma: PrismaService,
    private readonly supabase: SupabaseService,

  ) { }

  async listCompanies(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }): Promise<CompanyListResponseDto> {
    if (user.role !== 'super_admin') {
      throw new UnauthorizedException('Only super admins can view the list of companies');
    }

    const companies = await this.prisma.company.findMany({
      select: {
        id: true,
        name: true,
        created_at: true,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const total = await this.prisma.company.count();

    return { companies, total };
  }

  async createCompany(user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }, createDto: CreateCompanyDto): Promise<CompanyDto> {
    if (user.role !== 'super_admin') {
      throw new UnauthorizedException('Only super admins can create companies');
    }

    // Check if company with same name already exists
    const existingCompany = await this.prisma.company.findFirst({
      where: {
        name: createDto.name,
      },
    });

    if (existingCompany) {
      throw new BadRequestException('A company with this name already exists');
    }

    // Check if registration_number already exists
    const existingReg = await this.prisma.company.findFirst({
      where: {
        registration_number: createDto.registration_number,
      },
    });

    if (existingReg) {
      throw new BadRequestException('A company with this registration number already exists');
    }

    const created = await this.prisma.company.create({
      data: {
        name: createDto.name,
        registration_number: createDto.registration_number,
      },
    });

    return {
      ...created,
      id: typeof created.id === 'bigint' ? Number(created.id) : created.id,
    };
  }

  async listUsers(authUser: AuthUser) {
    const users = await this.prisma.users.findMany({
      select: {
        id: true,
        email: true,
        raw_user_meta_data: true,
      },
      where: {
        user_roles: {
          company_id: Number(authUser.company_id),
          role: { in: ["company_admin", "company_staff"] }
        }
      }
    });
    return {
      total: users.length,
      users
    };
  }

  async createCompanyAdmin(dto: CreateCompanyAdminDto) {
    // Create user in Supabase
    const { data, error } = await this.supabase.adminClient.auth.admin.createUser({
      email: dto.email,
      password: dto.password,
      user_metadata: {
        display_name: dto.name,
        phone: dto.phone,
        company_id: dto.company_id,
      },
      email_confirm: true,
    });
    if (error) throw new BadRequestException('Failed to create admin in Supabase: ' + error.message);
    // Optionally, create user_roles mapping in your DB
    await this.prisma.user_roles.create({
      data: {
        user_id: data.user.id,
        company_id: dto.company_id,
        role: dto.role ?? 'company_admin',
      },
    });

    return { message: 'Company admin created successfully', user_id: data.user.id };
  }

  async editCompanyAdmin(userId: string, dto: EditCompanyAdminDto) {
    const updates: any = {
      user_metadata: {},
    };
    if (dto.name) updates.user_metadata.display_name = dto.name;
    if (dto.phone) updates.user_metadata.phone = dto.phone;
    if (dto.email) updates.email = dto.email;
    if (dto.password) updates.password = dto.password;

    const { error } = await this.supabase.adminClient.auth.admin.updateUserById(userId, updates);
    if (error) throw new BadRequestException('Failed to update admin in Supabase: ' + error.message);

    return { message: 'Company admin updated successfully' };
  }
}