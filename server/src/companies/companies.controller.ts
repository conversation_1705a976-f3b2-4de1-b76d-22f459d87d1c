import { Body, Controller, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CompaniesService } from './companies.service';
import { CreateCompanyAdminDto, EditCompanyAdminDto } from './dto/company-admin.dto';
import { CompanyListResponseDto } from './dto/company-list.dto';
import { CreateCompanyDto } from './dto/create-company.dto';
import { CompanyDto } from './dto/company-list.dto';

@ApiTags('companies')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('companies')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) { }

  @Get()
  @Roles('super_admin')
  @ApiOperation({
    summary: 'List companies',
    description: 'Returns a list of all companies. Only accessible by super admins.'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of companies',
    type: CompanyListResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token'
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Only super admins can view the list of companies'
  })
  async listCompanies(@Req() req: any): Promise<CompanyListResponseDto> {
    return this.companiesService.listCompanies(req.user);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new company' })
  @ApiResponse({
    status: 201,
    description: 'The company has been successfully created',
    type: CompanyDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid company data or name already exists' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only super admins can create companies' })
  async createCompany(
    @Req() req: any,
    @Body() createDto: CreateCompanyDto,
  ): Promise<CompanyDto> {
    return this.companiesService.createCompany(req.user, createDto);
  }

  @Get('users')
  @Roles('company_admin', 'super_admin', 'company_staff')
  @ApiOperation({ summary: 'List users in company', description: 'List all users (employees) in the company. Company ID is taken from the authenticated user token.' })
  @ApiResponse({
    status: 200,
    description: 'List of users in the company.',
    schema: {
      example: {
        users: [
          {
            id: 'emp_123',
            email: '<EMAIL>',
          },
        ],
        total: 1,
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async listCompanyUsers(@Req() req): Promise<any> {
    // company_id is taken from the token
    return this.companiesService.listUsers(req.user);
  }

  @Post('admin')
  // @Roles('super_admin')
  @ApiOperation({ summary: 'Create company admin', description: 'Create a new company admin user assign to a company.' })
  @ApiResponse({ status: 201, description: 'Company admin created successfully', schema: { example: { message: 'Company admin created successfully', user_id: 'uuid' } } })
  @ApiResponse({ status: 400, description: 'Failed to create admin' })
  async createCompanyAdmin(@Body() dto: CreateCompanyAdminDto) {
    return this.companiesService.createCompanyAdmin(dto);
  }

  @Patch('admin/:userId')
  // @Roles('super_admin')
  @ApiOperation({ summary: 'Edit company admin', description: 'Edit an existing company admin user.' })
  @ApiResponse({ status: 200, description: 'Company admin updated successfully', schema: { example: { message: 'Company admin updated successfully' } } })
  @ApiResponse({ status: 400, description: 'Failed to update admin ' })
  async editCompanyAdmin(@Param('userId') userId: string, @Body() dto: EditCompanyAdminDto) {
    return this.companiesService.editCompanyAdmin(userId, dto);
  }
}