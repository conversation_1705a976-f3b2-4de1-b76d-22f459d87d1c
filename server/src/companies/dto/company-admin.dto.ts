import { ApiProperty } from '@nestjs/swagger';
import { user_role_type } from '@prisma/client';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsNumber, MinLength, IsIn } from 'class-validator';

export class CreateCompanyAdminDto {
    @ApiProperty({ example: 'Admin', description: 'Admin name' })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ example: '<EMAIL>', description: 'Admin email' })
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({ example: '9876543210', description: 'Admin phone number' })
    @IsString()
    @IsNotEmpty()
    phone: string;

    @ApiProperty({ example: 'securePassword123', description: 'Admin password' })
    @IsString()
    @MinLength(6)
    @IsNotEmpty()
    password: string;

    @ApiProperty({ example: 1, description: 'Company ID' })
    @IsNumber()
    @IsNotEmpty()
    company_id: number;

    @ApiProperty({ example: 'company_staff', description: 'role name' })
    @IsNotEmpty()
    @IsIn(['company_admin', 'company_staff'])
    role: user_role_type;
}

export class EditCompanyAdminDto {
    @ApiProperty({ example: 'Admin', description: 'Admin name', required: false })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiProperty({ example: '<EMAIL>', description: 'Admin email', required: false })
    @IsOptional()
    @IsEmail()
    email?: string;

    @ApiProperty({ example: '9876543210', description: 'Admin phone number', required: false })
    @IsOptional()
    @IsString()
    phone?: string;

    @ApiProperty({ example: 'securePassword123', description: 'Admin password', required: false })
    @IsOptional()
    @IsString()
    @MinLength(6)
    password?: string;
}