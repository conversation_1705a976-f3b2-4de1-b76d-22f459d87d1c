import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, <PERSON><PERSON>ength, MaxLength } from 'class-validator';

export class CreateCompanyDto {
  @ApiProperty({
    description: 'The name of the company',
    example: 'Acme Corporation',
    minLength: 2,
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Company name is required' })
  @IsString({ message: 'Company name must be a string' })
  @MinLength(2, { message: 'Company name must be at least 2 characters long' })
  @MaxLength(100, { message: 'Company name must not exceed 100 characters' })
  name: string;

  @ApiProperty({
    description: 'Company registration number',
    example: 'REG-2024-001',
    required: false,
  })
  @IsString({ message: 'Registration number must be a string' })
  @IsNotEmpty({ message: 'Registration number is required' })
  registration_number: string;
}