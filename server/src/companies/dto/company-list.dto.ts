import { ApiProperty } from '@nestjs/swagger';

export class CompanyDto {
  @ApiProperty({
    description: 'Company ID',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Company name',
    example: 'Acme Corp',
    nullable: true
  })
  name: string | null;

  @ApiProperty({
    description: 'Company creation date',
    example: '2024-03-20T10:00:00Z'
  })
  created_at: Date;
}

export class CompanyListResponseDto {
  @ApiProperty({
    description: 'List of companies',
    type: [CompanyDto]
  })
  companies: CompanyDto[];

  @ApiProperty({
    description: 'Total number of companies',
    example: 10
  })
  total: number;
} 