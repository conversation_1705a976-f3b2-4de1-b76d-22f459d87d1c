import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, Min<PERSON><PERSON><PERSON>, IsEnum, IsOptional, IsUUID } from 'class-validator';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  COMPANY_ADMIN = 'company_admin',
  EMPLOYEE = 'employee',
}

export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.COMPANY_ADMIN,
  })
  @IsEnum(UserRole)
  role: UserRole;

  @ApiProperty({
    description: 'Company ID (required for company_admin and employee roles)',
    example: '456e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  company_id?: string;

  @ApiProperty({
    description: 'Employee ID (required for employee role)',
    example: '789e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  employee_id?: string;
} 