import { ApiProperty, ApiExtraModels } from '@nestjs/swagger';

export type UserRoleType = 'super_admin' | 'company_admin' | 'employee' | 'company_staff';

export class BaseUserDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ example: '<EMAIL>', required: false })
  email?: string;

  @ApiProperty({ example: '<PERSON> Doe' })
  name: string;

  @ApiProperty({ enum: ['super_admin', 'company_admin', 'employee'], example: 'company_admin' })
  role: UserRoleType;
}

export class EmployeeUserDto extends BaseUserDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174001', required: false })
  employee_id?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174002', required: false })
  company_id?: string;
}

export class CompanyAdminUserDto extends BaseUserDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174002', required: false })
  company_id?: string;
}

export class SuperAdminUserDto extends BaseUserDto {}

export type UserResponseDto = EmployeeUserDto | CompanyAdminUserDto | SuperAdminUserDto;

@ApiExtraModels(EmployeeUserDto, CompanyAdminUserDto, SuperAdminUserDto)
export class AuthResponseDto {
  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  access_token: string;

  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  refresh_token: string;

  @ApiProperty({ type: BaseUserDto, description: 'User information. Can be EmployeeUserDto, CompanyAdminUserDto, or SuperAdminUserDto.' })
  user: UserResponseDto;
} 