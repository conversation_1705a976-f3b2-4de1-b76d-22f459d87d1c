import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, IsEnum } from 'class-validator';

export class SignInDto {
  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @ApiProperty({
    description: 'The password of the user',
    example: 'password123',
  })
  @IsString()
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;
}

export class SignUpDto {
  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @ApiProperty({
    description: 'The password of the user',
    example: 'password123',
  })
  @IsString()
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;

  @ApiProperty({
    description: 'The type of user',
    enum: ['employee', 'company_admin', 'super_admin'],
    example: 'employee',
  })
  @IsEnum(['employee', 'company_admin', 'super_admin'], {
    message: 'User type must be either employee, company_admin, or super_admin',
  })
  userType: 'employee' | 'company_admin' | 'super_admin';
}

export class UserDto {
  @ApiProperty({
    description: 'The unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'The name of the user',
    example: 'John Doe',
  })
  name: string;

  @ApiProperty({
    description: 'The role of the user',
    enum: ['super_admin', 'company_admin', 'employee'],
    example: 'employee',
  })
  role: 'super_admin' | 'company_admin' | 'employee';
}

export class AuthResponseDto {
  @ApiProperty({
    description: 'The access token for authentication',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  access_token: string;

  @ApiProperty({
    description: 'The refresh token for getting new access tokens',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refresh_token: string;

  @ApiProperty({
    description: 'The user information',
    type: UserDto,
  })
  user: UserDto;
} 