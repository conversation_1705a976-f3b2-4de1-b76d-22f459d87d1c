import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { SupabaseService } from '../supabase/supabase.service';
import { PrismaService } from '../prisma/prisma.service';

export interface RequestWithUser extends Request {
  user: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  };
}

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly prisma: PrismaService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<RequestWithUser>();
    
    // Log all headers for debugging
    console.log('Request headers:', request.headers);
    
    const token = this.extractTokenFromHeader(request);
    console.log('Extracted token:', token);

    if (!token) {
      console.error('No token provided in request');
      throw new UnauthorizedException('No token provided');
    }

    try {
      // Verify token with Supabase
      console.log('Attempting to verify token with Supabase:', token);
      const { data: { user }, error } = await this.supabaseService.client.auth.getUser(token);

      if (error || !user) {
        console.error('Supabase token verification failed:', error);
        throw new UnauthorizedException('Invalid token');
      }

      console.log('Token verified by Supabase, user:', user);

      // Get user role from database
      const userRole = await this.prisma.user_roles.findUnique({
        where: {
          user_id: user.id,
        },
        select: {
          role: true,
          company_id: true,
          employee_id: true,
        },
      });

      if (!userRole) {
        console.error('User role not found for user:', user.id);
        throw new UnauthorizedException('User role not found');
      }

      // Attach user info to request
      request.user = {
        sub: user.id,
        email: user.email || '',
        role: userRole.role,
        company_id: userRole.company_id?.toString(),
        employee_id: userRole.employee_id?.toString(),
      };

      console.log('User attached to request:', request.user);
      return true;
    } catch (error) {
      console.error('JWT guard error:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      throw new UnauthorizedException('Invalid token');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const authHeader = request.headers.authorization;
    console.log('Authorization header:', authHeader);
    
    if (!authHeader) {
      console.error('No authorization header found');
      return undefined;
    }
    
    const [type, token] = authHeader.split(' ');
    console.log('Auth type:', type);
    console.log('Token from header:', token);
    
    if (type !== 'Bearer') {
      console.error('Invalid authorization type:', type);
      return undefined;
    }

    return token;
  }
} 