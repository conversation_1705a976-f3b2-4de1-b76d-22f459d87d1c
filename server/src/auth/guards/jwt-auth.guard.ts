import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { SupabaseService } from '../../supabase/supabase.service';
import { PrismaService } from '../../prisma/prisma.service';
import { Request } from 'express';

interface RequestWithUser extends Request {
  user: {
    id: string;
    email: string;
    role: string;
    company_id?: number;
    employee_id?: string;
  };
}

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly prisma: PrismaService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<RequestWithUser>();
    const authHeader = request.headers['authorization'];
    const token = authHeader?.split(' ')[1];

    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      // Verify token with Supabase
      const { data: { user }, error } = await this.supabaseService.client.auth.getUser(token);

      if (error || !user) {
        throw new UnauthorizedException('Invalid or expired token');
      }

      // Get user role from database
      const userRole = await this.prisma.user_roles.findUnique({
        where: {
          user_id: user.id,
        },
        select: {
          role: true,
          company_id: true,
          employee_id: true,
        },
      });

      if (!userRole) {
        throw new UnauthorizedException('User role not found');
      }

      // Attach user info to request
      request.user = {
        id: user.id,
        email: user.email || '',
        role: userRole.role,
        company_id: userRole.company_id || undefined,
        employee_id: userRole.employee_id || undefined,
      };

      return true;
    } catch (error) {
      console.log(error);
      throw new UnauthorizedException('Invalid token');
    }
  }
} 