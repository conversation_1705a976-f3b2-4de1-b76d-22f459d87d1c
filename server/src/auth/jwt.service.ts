import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService as NestJwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtService {
  constructor(
    private readonly jwtService: NestJwtService,
    private readonly configService: ConfigService,
  ) {
    // Log when service is initialized
    const secret = this.configService.get<string>('JWT_SECRET');
    console.log('JWT Service initialized with secret:', !!secret);
  }

  generateToken(payload: {
    sub: string;
    email: string;
    role: string;
    company_id?: string;
    employee_id?: string;
  }) {
    const secret = this.configService.get<string>('JWT_SECRET');
    if (!secret) {
      console.error('JWT_SECRET is not configured');
      throw new Error('JWT_SECRET is not configured');
    }

    console.log('Generating token with payload:', JSON.stringify(payload, null, 2));
    const token = this.jwtService.sign(payload, {
      secret,
      expiresIn: '1d',
      algorithm: 'HS256',
    });
    console.log('Generated token:', token);
    
    // Verify the token immediately after generation
    try {
      const verified = this.jwtService.verify(token, { secret });
      console.log('Token verified after generation:', !!verified);
    } catch (error) {
      console.error('Token verification failed after generation:', error.message);
    }
    
    return token;
  }

  verifyToken(token: string) {
    try {
      console.log('Attempting to verify token:', token);
      const secret = this.configService.get<string>('JWT_SECRET');
      if (!secret) {
        console.error('JWT_SECRET is not configured during verification');
        throw new Error('JWT_SECRET is not configured');
      }

      console.log('Using secret for verification:', !!secret);
      const payload = this.jwtService.verify(token, {
        secret,
        algorithms: ['HS256'],
      });
      console.log('Token payload:', JSON.stringify(payload, null, 2));

      // Validate required fields
      if (!payload.sub || !payload.email || !payload.role) {
        console.error('Missing required fields in token payload:', payload);
        throw new UnauthorizedException('Invalid token payload');
      }

      return payload;
    } catch (error) {
      console.error('Token verification error:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      if (error.name === 'JsonWebTokenError') {
        console.error('JWT Error:', error.message);
      } else if (error.name === 'TokenExpiredError') {
        console.error('Token expired:', error.message);
      }
      return null;
    }
  }
} 