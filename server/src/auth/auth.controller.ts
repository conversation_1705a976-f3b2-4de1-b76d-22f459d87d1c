import { Controller, Post, Body, UseGuards, Req, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { JwtAuthGuard } from './jwt-auth.guard';
import { AuthResponseDto, EmployeeUserDto, CompanyAdminUserDto, SuperAdminUserDto } from './dto/auth-response.dto';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ 
    summary: 'User login',
    description: 'Authenticates a user and returns their information along with access and refresh tokens. The user information returned depends on the user role.'
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Invalid credentials or user not found' 
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data' 
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    const { email, password } = loginDto;
    return this.authService.login(email, password);
  }

  @Post('register')
  @ApiOperation({ 
    summary: 'User registration',
    description: 'Registers a new user with the specified role. The user will need to verify their email before they can log in.'
  })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data or missing required fields' 
  })
  @ApiResponse({ 
    status: 409, 
    description: 'User already exists' 
  })
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
    const { email, password, role, company_id, employee_id } = registerDto;
    return this.authService.register(email, password, '', role, company_id, employee_id);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'User logout',
    description: 'Logs out the current user and invalidates their session.'
  })
  @ApiResponse({
    status: 200,
    description: 'Logout successful',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Logged out successfully'
        }
      }
    }
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  async logout() {
    return this.authService.logout();
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get current user',
    description: 'Returns the current user information. The exact properties depend on the user role.'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the current user information',
    schema: {
      oneOf: [
        { $ref: '#/components/schemas/EmployeeUserDto' },
        { $ref: '#/components/schemas/CompanyAdminUserDto' },
        { $ref: '#/components/schemas/SuperAdminUserDto' }
      ]
    }
  })
  @ApiResponse({ 
    status: 401, 
    description: 'Unauthorized - Invalid or missing token' 
  })
  @ApiResponse({ 
    status: 404, 
    description: 'User role not found' 
  })
  async getCurrentUser(@Req() req) {
    return this.authService.getCurrentUser(req);
  }
} 