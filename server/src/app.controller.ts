import { Controller, Get } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AppService } from './app.service';
import { formatInTimeZone } from 'date-fns-tz';

@ApiTags('App')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  // @Get()
  // @ApiOperation({ summary: 'Hello World', description: 'Returns Hello World string.' })
  // @ApiOkResponse({ description: 'Hello World string.', schema: { example: 'Hello World!' } })
  // getHello(): string {
  //   return this.appService.getHello();
  // }

  @Get('current-time')
  @ApiOperation({ summary: 'Current Time', description: 'Returns the current server time.' })
  @ApiOkResponse({
    description: 'Current server time.',
    schema: { example: { time: '2025-05-22T12:34:56.789Z' } }
  })
  currentTime() {
    return {
      time: new Date()
    }
  }

  @Get('current-time-format')
  @ApiOperation({ summary: 'Current Time', description: 'Returns the current server time.' })
  @ApiOkResponse({
    description: 'Current server time.',
    schema: { example: { time: '2025-05-22T12:34:56.789Z' } }
  })
  currentTimeTo() {
    const now = new Date();

    // Get timezone from environment variable, fallback to system timezone
    const timezone = process.env.TIMEZONE || Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Format to specified timezone in the desired format
    const date = formatInTimeZone(now, timezone, 'yyyy-MM-dd hh:mm a');
    return {
      time: new Date(),
      date,
      timezone:Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }
}
