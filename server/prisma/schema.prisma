generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["multiSchem  a"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["auth", "public"]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model audit_log_entries {
  instance_id String?   @db.Uuid
  id          String    @id @db.Uuid
  payload     Json?     @db.<PERSON><PERSON>
  created_at  DateTime? @db.Timestamptz(6)
  ip_address  String    @default("") @db.VarChar(64)

  @@index([instance_id], map: "audit_logs_instance_id_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model flow_state {
  id                     String                @id @db.Uuid
  user_id                String?               @db.Uuid
  auth_code              String
  code_challenge_method  code_challenge_method
  code_challenge         String
  provider_type          String
  provider_access_token  String?
  provider_refresh_token String?
  created_at             DateTime?             @db.Timestamptz(6)
  updated_at             DateTime?             @db.Timestamptz(6)
  authentication_method  String
  auth_code_issued_at    DateTime?             @db.Timestamptz(6)
  saml_relay_states      saml_relay_states[]

  @@index([created_at(sort: Desc)])
  @@index([auth_code], map: "idx_auth_code")
  @@index([user_id, authentication_method], map: "idx_user_id_auth_method")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model identities {
  provider_id     String
  user_id         String    @db.Uuid
  identity_data   Json
  provider        String
  last_sign_in_at DateTime? @db.Timestamptz(6)
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)
  email           String?   @default(dbgenerated("lower((identity_data ->> 'email'::text))"))
  id              String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  users           users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([provider_id, provider], map: "identities_provider_id_provider_unique")
  @@index([email])
  @@index([user_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model instances {
  id              String    @id @db.Uuid
  uuid            String?   @db.Uuid
  raw_base_config String?
  created_at      DateTime? @db.Timestamptz(6)
  updated_at      DateTime? @db.Timestamptz(6)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_amr_claims {
  session_id            String   @db.Uuid
  created_at            DateTime @db.Timestamptz(6)
  updated_at            DateTime @db.Timestamptz(6)
  authentication_method String
  id                    String   @id(map: "amr_id_pk") @db.Uuid
  sessions              sessions @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([session_id, authentication_method], map: "mfa_amr_claims_session_id_authentication_method_pkey")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_challenges {
  id                     String      @id @db.Uuid
  factor_id              String      @db.Uuid
  created_at             DateTime    @db.Timestamptz(6)
  verified_at            DateTime?   @db.Timestamptz(6)
  ip_address             String      @db.Inet
  otp_code               String?
  web_authn_session_data Json?
  mfa_factors            mfa_factors @relation(fields: [factor_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "mfa_challenges_auth_factor_id_fkey")

  @@index([created_at(sort: Desc)], map: "mfa_challenge_created_at_idx")
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model mfa_factors {
  id                   String           @id @db.Uuid
  user_id              String           @db.Uuid
  friendly_name        String?
  factor_type          factor_type
  status               factor_status
  created_at           DateTime         @db.Timestamptz(6)
  updated_at           DateTime         @db.Timestamptz(6)
  secret               String?
  phone                String?
  last_challenged_at   DateTime?        @unique @db.Timestamptz(6)
  web_authn_credential Json?
  web_authn_aaguid     String?          @db.Uuid
  mfa_challenges       mfa_challenges[]
  users                users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, phone], map: "unique_phone_factor_per_user")
  @@index([user_id, created_at], map: "factor_id_created_at_idx")
  @@index([user_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model one_time_tokens {
  id         String              @id @db.Uuid
  user_id    String              @db.Uuid
  token_type one_time_token_type
  token_hash String
  relates_to String
  created_at DateTime            @default(now()) @db.Timestamp(6)
  updated_at DateTime            @default(now()) @db.Timestamp(6)
  users      users               @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, token_type])
  @@index([relates_to], map: "one_time_tokens_relates_to_hash_idx", type: Hash)
  @@index([token_hash], map: "one_time_tokens_token_hash_hash_idx", type: Hash)
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model refresh_tokens {
  instance_id String?   @db.Uuid
  id          BigInt    @id @default(autoincrement())
  token       String?   @unique(map: "refresh_tokens_token_unique") @db.VarChar(255)
  user_id     String?   @db.VarChar(255)
  revoked     Boolean?
  created_at  DateTime? @db.Timestamptz(6)
  updated_at  DateTime? @db.Timestamptz(6)
  parent      String?   @db.VarChar(255)
  session_id  String?   @db.Uuid
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([instance_id])
  @@index([instance_id, user_id])
  @@index([parent])
  @@index([session_id, revoked])
  @@index([updated_at(sort: Desc)])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_providers {
  id                String        @id @db.Uuid
  sso_provider_id   String        @db.Uuid
  entity_id         String        @unique
  metadata_xml      String
  metadata_url      String?
  attribute_mapping Json?
  created_at        DateTime?     @db.Timestamptz(6)
  updated_at        DateTime?     @db.Timestamptz(6)
  name_id_format    String?
  sso_providers     sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model saml_relay_states {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  request_id      String
  for_email       String?
  redirect_to     String?
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  flow_state_id   String?       @db.Uuid
  flow_state      flow_state?   @relation(fields: [flow_state_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([created_at(sort: Desc)])
  @@index([for_email])
  @@index([sso_provider_id])
  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schema_migrations {
  version String @id @db.VarChar(255)

  @@schema("auth")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model sessions {
  id             String           @id @db.Uuid
  user_id        String           @db.Uuid
  created_at     DateTime?        @db.Timestamptz(6)
  updated_at     DateTime?        @db.Timestamptz(6)
  factor_id      String?          @db.Uuid
  aal            aal_level?
  not_after      DateTime?        @db.Timestamptz(6)
  refreshed_at   DateTime?        @db.Timestamp(6)
  user_agent     String?
  ip             String?          @db.Inet
  tag            String?
  mfa_amr_claims mfa_amr_claims[]
  refresh_tokens refresh_tokens[]
  users          users            @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([not_after(sort: Desc)])
  @@index([user_id])
  @@index([user_id, created_at], map: "user_id_created_at_idx")
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_domains {
  id              String        @id @db.Uuid
  sso_provider_id String        @db.Uuid
  domain          String
  created_at      DateTime?     @db.Timestamptz(6)
  updated_at      DateTime?     @db.Timestamptz(6)
  sso_providers   sso_providers @relation(fields: [sso_provider_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([sso_provider_id])
  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model sso_providers {
  id                String              @id @db.Uuid
  resource_id       String?
  created_at        DateTime?           @db.Timestamptz(6)
  updated_at        DateTime?           @db.Timestamptz(6)
  saml_providers    saml_providers[]
  saml_relay_states saml_relay_states[]
  sso_domains       sso_domains[]

  @@schema("auth")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model users {
  instance_id                 String?           @db.Uuid
  id                          String            @id @db.Uuid
  aud                         String?           @db.VarChar(255)
  role                        String?           @db.VarChar(255)
  email                       String?           @db.VarChar(255)
  encrypted_password          String?           @db.VarChar(255)
  email_confirmed_at          DateTime?         @db.Timestamptz(6)
  invited_at                  DateTime?         @db.Timestamptz(6)
  confirmation_token          String?           @db.VarChar(255)
  confirmation_sent_at        DateTime?         @db.Timestamptz(6)
  recovery_token              String?           @db.VarChar(255)
  recovery_sent_at            DateTime?         @db.Timestamptz(6)
  email_change_token_new      String?           @db.VarChar(255)
  email_change                String?           @db.VarChar(255)
  email_change_sent_at        DateTime?         @db.Timestamptz(6)
  last_sign_in_at             DateTime?         @db.Timestamptz(6)
  raw_app_meta_data           Json?
  raw_user_meta_data          Json?
  is_super_admin              Boolean?
  created_at                  DateTime?         @db.Timestamptz(6)
  updated_at                  DateTime?         @db.Timestamptz(6)
  phone                       String?           @unique
  phone_confirmed_at          DateTime?         @db.Timestamptz(6)
  phone_change                String?           @default("")
  phone_change_token          String?           @default("") @db.VarChar(255)
  phone_change_sent_at        DateTime?         @db.Timestamptz(6)
  confirmed_at                DateTime?         @default(dbgenerated("LEAST(email_confirmed_at, phone_confirmed_at)")) @db.Timestamptz(6)
  email_change_token_current  String?           @default("") @db.VarChar(255)
  email_change_confirm_status Int?              @default(0) @db.SmallInt
  banned_until                DateTime?         @db.Timestamptz(6)
  reauthentication_token      String?           @default("") @db.VarChar(255)
  reauthentication_sent_at    DateTime?         @db.Timestamptz(6)
  is_sso_user                 Boolean           @default(false)
  deleted_at                  DateTime?         @db.Timestamptz(6)
  is_anonymous                Boolean           @default(false)
  identities                  identities[]
  mfa_factors                 mfa_factors[]
  one_time_tokens             one_time_tokens[]
  sessions                    sessions[]
  schedules                   schedules[]
  user_roles                  user_roles?

  @@index([instance_id])
  @@index([is_anonymous])
  @@schema("auth")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model company {
  name                String?           @db.VarChar
  created_at          DateTime          @default(now()) @db.Timestamptz(6)
  id                  Int               @id @unique @default(autoincrement())
  registration_number String?
  extra_pay_rate      Decimal?          @db.Decimal
  employee            employee[]
  schedule_logs       schedule_logs[]
  schedules           schedules[]
  tax_rates           tax_rates[]
  time_sheets         time_sheets[]
  time_slot_rates     time_slot_rates[]
  user_roles          user_roles[]

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model employee {
  id                  String          @id @default(dbgenerated("auth.uid()")) @db.Uuid
  created_at          DateTime        @default(now()) @db.Timestamptz(6)
  name                String?         @db.VarChar
  phone               String?         @db.VarChar
  company_id          Int
  email_address       String?         @db.VarChar
  employee_number     String?         @db.VarChar
  first_name          String?         @db.VarChar
  last_name           String?         @db.VarChar
  has_driving_license Boolean?        @default(false)
  company             company         @relation(fields: [company_id], references: [id], onUpdate: NoAction)
  schedule_logs       schedule_logs[]
  schedules           schedules[]
  time_sheets         time_sheets[]
  user_roles          user_roles[]

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model user_roles {
  user_id     String         @id @db.Uuid
  role        user_role_type
  company_id  Int?
  employee_id String?        @db.Uuid
  company     company?       @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  employee    employee?      @relation(fields: [employee_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users       users          @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model time_slot_rates {
  id                      Int                       @id @default(autoincrement())
  company_id              Int?
  created_at              DateTime                  @default(now()) @db.Timestamptz(6)
  name                    String?                   @db.VarChar
  start_time              DateTime                  @db.Time(6)
  end_time                DateTime                  @db.Time(6)
  break_time              Int                       @default(0)
  hourly_rate             Decimal?                  @default(0) @db.Decimal
  is_base_price           Boolean?                  @default(false)
  rate                    Decimal?                  @default(0) @db.Decimal
  schedule_log_time_slots schedule_log_time_slots[]
  company                 company?                  @relation(fields: [company_id], references: [id], onDelete: Restrict, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schedules {
  id            Int              @id @default(autoincrement())
  company_id    Int?
  employee_id   String?          @db.Uuid
  type          schedule_type?
  is_finished   Boolean?         @default(false)
  note          String?          @db.VarChar
  start_date    DateTime?        @db.Timestamptz(6)
  end_date      DateTime?        @db.Timestamptz(6)
  start_time    DateTime?        @db.Time(6)
  end_time      DateTime?        @db.Time(6)
  created_at    DateTime         @default(now()) @db.Timestamptz(6)
  duration      Int?             @default(0)
  status        schedule_status? @default(assigned)
  created_by    String?          @db.Uuid
  schedule_logs schedule_logs[]
  company       company?         @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user          users?           @relation(fields: [created_by], references: [id], onDelete: NoAction, onUpdate: NoAction)
  employee      employee?        @relation(fields: [employee_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schedule_logs {
  id                 Int                       @id @default(autoincrement())
  schedule_id        Int?
  company_id         Int?
  employee_id        String?                   @db.Uuid
  status             schedule_log_status?
  clock_in_at        DateTime?                 @db.Timestamptz(6)
  clock_out_at       DateTime?                 @db.Timestamptz(6)
  note               String?
  admin_note         String?
  duration           Float?                    @default(0)
  created_at         DateTime                  @default(now()) @db.Timestamptz(6)
  approved_at        DateTime?                 @db.Timestamptz(6)
  work_duration      Int?                      @default(0)
  overtime_duration  Int?                      @default(0)
  total_pay          Decimal?                  @default(0) @db.Decimal
  overtime_pay       Decimal?                  @default(0) @db.Decimal
  work_pay           Decimal?                  @default(0) @db.Decimal
  time_sheet_updated Boolean?                  @default(false)
  time_sheet_id      Int?
  break_time         Int?                      @default(0)
  work_pay_split     Json[]
  overtime_pay_split Json[]
  break_start        DateTime?                 @db.Timestamptz(6)
  break_end          DateTime?                 @db.Timestamptz(6)
  extra_pay          Decimal?                  @db.Decimal
  time_slots         schedule_log_time_slots[]
  company            company?                  @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  employee           employee?                 @relation(fields: [employee_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  schedules          schedules?                @relation(fields: [schedule_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  time_sheets        time_sheets?              @relation(fields: [time_sheet_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model time_sheets {
  id                      Int                @id @default(autoincrement())
  employee_id             String?            @db.Uuid
  company_id              Int?
  period                  DateTime?          @db.Date
  total_duration_allotted Int?               @default(0)
  total_duration          Int?               @default(0)
  total_duration_worked   Int?               @default(0)
  total_duration_overtime Int?               @default(0)
  total_pay               Decimal?           @default(0) @db.Decimal
  total_pay_worked        Decimal?           @default(0) @db.Decimal
  total_pay_overtime      Decimal?           @default(0) @db.Decimal
  status                  time_sheet_status? @default(not_paid)
  payment_type            payment_types?
  paid_date               DateTime?          @db.Timestamptz(6)
  meta                    Json?              @db.Json
  note                    String?            @db.VarChar
  created_at              DateTime           @default(now()) @db.Timestamptz(6)
  tax_meta                Json?              @db.Json
  deduction               Decimal?           @default(0) @db.Decimal
  final_pay               Decimal?           @db.Decimal
  period_from             DateTime?          @db.Date
  period_to               DateTime?          @db.Date
  extra_pay_total         Decimal?           @db.Decimal
  schedule_logs           schedule_logs[]
  company                 company?           @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  employee                employee?          @relation(fields: [employee_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model schedule_log_time_slots {
  id                Int              @id @default(autoincrement())
  created_at        DateTime         @default(now()) @db.Timestamptz(6)
  schedule_log_id   Int?
  time_slot_rate_id Int?
  break_time        Decimal?         @db.Decimal
  hours_worked      Decimal?         @db.Decimal
  start             DateTime?        @db.Timestamptz(6)
  end               DateTime?        @db.Timestamptz(6)
  rate              Decimal?         @db.Decimal
  amount            Decimal?         @db.Decimal
  schedule_log      schedule_logs?   @relation(fields: [schedule_log_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  time_slot_rates   time_slot_rates? @relation(fields: [time_slot_rate_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model tax_rates {
  id         Int      @id @default(autoincrement())
  company_id Int?
  percentage Decimal? @db.Decimal
  created_at DateTime @default(now()) @db.Timestamptz(6)
  company    company? @relation(fields: [company_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@schema("public")
}

enum aal_level {
  aal1
  aal2
  aal3

  @@schema("auth")
}

enum code_challenge_method {
  s256
  plain

  @@schema("auth")
}

enum factor_status {
  unverified
  verified

  @@schema("auth")
}

enum factor_type {
  totp
  webauthn
  phone

  @@schema("auth")
}

enum one_time_token_type {
  confirmation_token
  reauthentication_token
  recovery_token
  email_change_token_new
  email_change_token_current
  phone_change_token

  @@schema("auth")
}

enum user_role_type {
  super_admin
  company_admin
  employee
  company_staff

  @@schema("public")
}

enum schedule_type {
  single_day
  weekly
  monthly

  @@schema("public")
}

enum schedule_log_status {
  started
  completed
  pending
  approved
  rejected

  @@schema("public")
}

enum payment_types {
  direct_deposit
  cheque
  paypal
  wire_transfer
  other

  @@schema("public")
}

enum time_sheet_status {
  paid
  not_paid

  @@schema("public")
}

enum schedule_status {
  assigned
  ongoing
  completed

  @@schema("public")
}
